using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

[TestTimeout(TestTimeouts.Unit)]
[Trait("Category", TestCategories.Unit)]
public class IndexRegimeServiceTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<ITickStreamService> _mockTickStreamService;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly Mock<ILogger<IndexRegimeService>> _mockLogger;
    private readonly IConfiguration _configuration;
    private readonly IndexRegimeService _service;

    public IndexRegimeServiceTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockTickStreamService = new Mock<ITickStreamService>();
        _mockRedis = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<IndexRegimeService>>();

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["IndexRegime:SpxMomentumPeriodMinutes"] = "1440",
                ["IndexRegime:VixVolatileThreshold"] = "25.0",
                ["IndexRegime:VixPanicThreshold"] = "35.0",
                ["IndexRegime:VixEuphoricThreshold"] = "12.0",
                ["IndexRegime:SpxMomentumBullThreshold"] = "0.5",
                ["IndexRegime:SpxMomentumBearThreshold"] = "-0.5",
                ["IndexRegime:DivergenceThreshold"] = "2.0",
                ["IndexRegime:RegimeConfirmationMinutes"] = "15",
                ["IndexRegime:CacheExpiryMinutes"] = "5",
                ["IndexRegime:EnableRealTimeUpdates"] = "true"
            })
            .Build();

        _mockRedisService.Setup(x => x.GetDatabaseAsync()).ReturnsAsync(_mockRedis.Object);

        _service = new IndexRegimeService(
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockTickStreamService.Object,
            _configuration,
            _mockLogger.Object);
    }

    [Fact]
    public async Task StartMonitoringAsync_ShouldStartSuccessfully()
    {
        // Act
        await _service.StartMonitoringAsync();

        // Assert
        _service.GetStatus().Should().Be(IndexRegimeMonitorStatus.Active);
    }

    [Fact]
    public async Task StopMonitoringAsync_ShouldStopSuccessfully()
    {
        // Arrange
        await _service.StartMonitoringAsync();

        // Act
        await _service.StopMonitoringAsync();

        // Assert
        _service.GetStatus().Should().Be(IndexRegimeMonitorStatus.Stopped);
    }

    [Fact]
    public async Task GetSpxMomentumAsync_WithValidData_ShouldReturnAnalysis()
    {
        // Arrange
        var bars = CreateMockSpxBars();
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(bars);

        // Act
        var result = await _service.GetSpxMomentumAsync();

        // Assert
        result.Should().NotBeNull();
        result.CurrentPrice.Should().BeGreaterThan(0);
        result.PreviousPrice.Should().BeGreaterThan(0);
        result.Direction.Should().BeOneOf(MomentumDirection.Bullish, MomentumDirection.Bearish, MomentumDirection.Neutral);
        result.Strength.Should().BeOneOf(MomentumStrength.Weak, MomentumStrength.Moderate, MomentumStrength.Strong, MomentumStrength.Extreme);
    }

    [Fact]
    public async Task GetVixAnalysisAsync_WithValidData_ShouldReturnAnalysis()
    {
        // Arrange
        var bars = CreateMockVixBars();
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(bars);

        // Act
        var result = await _service.GetVixAnalysisAsync();

        // Assert
        result.Should().NotBeNull();
        result.CurrentVix.Should().BeGreaterThan(0);
        result.Level.Should().BeOneOf(VixLevel.VeryLow, VixLevel.Low, VixLevel.Normal, VixLevel.Elevated, VixLevel.High, VixLevel.VeryHigh, VixLevel.Extreme);
        result.Trend.Should().BeOneOf(VixTrend.Declining, VixTrend.Stable, VixTrend.Rising, VixTrend.Spiking);
    }

    [Fact]
    public async Task GetSpxVixDivergenceAsync_ShouldCalculateDivergence()
    {
        // Arrange
        var spxBars = CreateMockSpxBars();
        var vixBars = CreateMockVixBars();
        
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);

        // Act
        var result = await _service.GetSpxVixDivergenceAsync();

        // Assert
        result.Should().NotBeNull();
        result.DivergenceType.Should().BeOneOf(DivergenceType.Bullish, DivergenceType.Bearish, DivergenceType.Neutral);
        result.Significance.Should().BeOneOf(DivergenceSignificance.Insignificant, DivergenceSignificance.Mild, DivergenceSignificance.Moderate, DivergenceSignificance.Strong, DivergenceSignificance.Extreme);
    }

    [Fact]
    public async Task GetNdxMomentumAsync_WithValidData_ShouldReturnAnalysis()
    {
        // Arrange
        var ndxBars = CreateMockNdxBars();
        var spxBars = CreateMockSpxBars();
        
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "NDX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(ndxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);

        // Act
        var result = await _service.GetNdxMomentumAsync();

        // Assert
        result.Should().NotBeNull();
        result.CurrentPrice.Should().BeGreaterThan(0);
        result.Direction.Should().BeOneOf(MomentumDirection.Bullish, MomentumDirection.Bearish, MomentumDirection.Neutral);
        result.TechStrength.Should().BeOneOf(TechSectorStrength.Underperforming, TechSectorStrength.InLine, TechSectorStrength.Outperforming, TechSectorStrength.Leading);
    }

    [Fact]
    public async Task RefreshRegimeAsync_ShouldClassifyRegimeCorrectly()
    {
        // Arrange
        var spxBars = CreateMockSpxBars();
        var vixBars = CreateMockVixBars();
        var ndxBars = CreateMockNdxBars();
        
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "NDX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(ndxBars);

        // Act
        var result = await _service.RefreshRegimeAsync();

        // Assert
        result.Should().BeOneOf(IndexMarketRegime.TrendingUp, IndexMarketRegime.TrendingDown, IndexMarketRegime.Sideways, 
            IndexMarketRegime.Volatile, IndexMarketRegime.Panic, IndexMarketRegime.Euphoric);
    }

    [Fact]
    public async Task GetCachedRegimeAsync_WithValidCache_ShouldReturnCachedRegime()
    {
        // Arrange
        var cachedRegime = new RedisIndexRegime
        {
            Regime = IndexMarketRegime.TrendingUp,
            DetectedAt = DateTime.UtcNow.AddMinutes(-2),
            Confidence = 0.8m
        };

        _mockRedis.Setup(x => x.StringGetAsync(RedisIndexRegime.GetRedisKey(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(cachedRegime.ToJson());

        // Act
        var result = await _service.GetCachedRegimeAsync();

        // Assert
        result.Should().Be(IndexMarketRegime.TrendingUp);
    }

    [Fact]
    public async Task GetRegimeHistoryAsync_ShouldReturnHistoricalData()
    {
        // Arrange
        var historicalRegime = new RedisIndexRegime
        {
            Regime = IndexMarketRegime.TrendingUp,
            DetectedAt = DateTime.UtcNow.AddHours(-1),
            Confidence = 0.7m
        };

        _mockRedis.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(historicalRegime.ToJson());

        // Act
        var result = await _service.GetRegimeHistoryAsync(2);

        // Assert
        result.Should().NotBeNull();
        // Note: In a real implementation, this would return actual historical data
    }

    [Theory]
    [InlineData(40.0, IndexMarketRegime.Panic)]
    [InlineData(30.0, IndexMarketRegime.Volatile)]
    [InlineData(20.0, IndexMarketRegime.Sideways)]
    [InlineData(10.0, IndexMarketRegime.Euphoric)]
    public async Task RefreshRegimeAsync_WithDifferentVixLevels_ShouldClassifyCorrectly(double vixLevel, IndexMarketRegime expectedRegime)
    {
        // Arrange
        var spxBars = CreateMockSpxBars(momentum: 0.1m); // Slight positive momentum
        var vixBars = CreateMockVixBars((decimal)vixLevel);
        var ndxBars = CreateMockNdxBars();
        
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "NDX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(ndxBars);

        // Act
        var result = await _service.RefreshRegimeAsync();

        // Assert
        result.Should().Be(expectedRegime);
    }

    private List<IIndexBar> CreateMockSpxBars(decimal momentum = 0.5m)
    {
        var basePrice = 4500m;
        var bars = new List<IIndexBar>();
        
        for (int i = 0; i < 10; i++)
        {
            var price = basePrice + (i * momentum * 10); // Apply momentum
            var mockBar = new Mock<IIndexBar>();
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - 5);
            mockBar.Setup(x => x.High).Returns(price + 10);
            mockBar.Setup(x => x.Low).Returns(price - 10);
            mockBar.Setup(x => x.Timestamp).Returns(DateTime.UtcNow.AddDays(-i));
            bars.Add(mockBar.Object);
        }
        
        return bars.OrderBy(b => b.Timestamp).ToList();
    }

    private List<IIndexBar> CreateMockVixBars(decimal vixLevel = 20m)
    {
        var bars = new List<IIndexBar>();
        
        for (int i = 0; i < 10; i++)
        {
            var mockBar = new Mock<IIndexBar>();
            mockBar.Setup(x => x.Close).Returns(vixLevel + (i % 3 - 1)); // Small variations
            mockBar.Setup(x => x.Open).Returns(vixLevel);
            mockBar.Setup(x => x.High).Returns(vixLevel + 2);
            mockBar.Setup(x => x.Low).Returns(vixLevel - 2);
            mockBar.Setup(x => x.Timestamp).Returns(DateTime.UtcNow.AddDays(-i));
            bars.Add(mockBar.Object);
        }
        
        return bars.OrderBy(b => b.Timestamp).ToList();
    }

    private List<IIndexBar> CreateMockNdxBars()
    {
        var basePrice = 15000m;
        var bars = new List<IIndexBar>();
        
        for (int i = 0; i < 10; i++)
        {
            var price = basePrice + (i * 50); // Slight upward trend
            var mockBar = new Mock<IIndexBar>();
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - 25);
            mockBar.Setup(x => x.High).Returns(price + 50);
            mockBar.Setup(x => x.Low).Returns(price - 50);
            mockBar.Setup(x => x.Timestamp).Returns(DateTime.UtcNow.AddDays(-i));
            bars.Add(mockBar.Object);
        }
        
        return bars.OrderBy(b => b.Timestamp).ToList();
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
