using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for SecEventFilterService
/// Tests SEC filing analysis, event filtering, and market event detection
/// </summary>
public class SecEventFilterServiceTests
{
    private readonly Mock<ILogger<SecEventFilterService>> _mockLogger;
    private readonly SecEventFilterService _service;

    public SecEventFilterServiceTests()
    {
        _mockLogger = new Mock<ILogger<SecEventFilterService>>();
        var mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _service = new SecEventFilterService(_mockLogger.Object, mockHttpClientFactory.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetUpcomingEventsAsync_WithValidSymbol_ShouldReturnEvents()
    {
        // Arrange
        var symbol = "AAPL";
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act
        var events = await _service.GetUpcomingEventsAsync(symbol, timePeriod);

        // Assert
        events.Should().NotBeNull();
        // Note: This is a placeholder implementation, so we expect empty results
        events.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetUpcomingEventsAsync_WithNullSymbol_ShouldThrowArgumentException()
    {
        // Arrange
        string symbol = null!;
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.GetUpcomingEventsAsync(symbol, timePeriod));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetUpcomingEventsAsync_WithEmptySymbol_ShouldThrowArgumentException()
    {
        // Arrange
        var symbol = "";
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.GetUpcomingEventsAsync(symbol, timePeriod));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Theory]
    [InlineData(SmaEventTimePeriod.Next24Hours)]
    [InlineData(SmaEventTimePeriod.NextWeek)]
    [InlineData(SmaEventTimePeriod.NextMonth)]
    [InlineData(SmaEventTimePeriod.Next3Days)]
    public async Task GetUpcomingEventsAsync_WithAllTimePeriods_ShouldHandleCorrectly(SmaEventTimePeriod timePeriod)
    {
        // Arrange
        var symbol = "MSFT";

        // Act
        var events = await _service.GetUpcomingEventsAsync(symbol, timePeriod);

        // Assert
        events.Should().NotBeNull();
        // Placeholder implementation returns empty results
        events.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFilingsAsync_WithValidSymbol_ShouldReturnFilings()
    {
        // Arrange
        var symbol = "TSLA";
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act
        var filings = await _service.GetRecentFilingsAsync(symbol, timePeriod);

        // Assert
        filings.Should().NotBeNull();
        // Note: This is a placeholder implementation, so we expect empty results
        filings.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFilingsAsync_WithNullSymbol_ShouldThrowArgumentException()
    {
        // Arrange
        string symbol = null!;
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _service.GetRecentFilingsAsync(symbol, timePeriod));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFilingsAsync_WithEmptySymbol_ShouldThrowArgumentException()
    {
        // Arrange
        var symbol = "";
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _service.GetRecentFilingsAsync(symbol, timePeriod));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Theory]
    [InlineData(SmaEventTimePeriod.Past24Hours)]
    [InlineData(SmaEventTimePeriod.PastWeek)]
    [InlineData(SmaEventTimePeriod.PastMonth)]
    [InlineData(SmaEventTimePeriod.Past3Days)]
    public async Task GetRecentFilingsAsync_WithAllTimePeriods_ShouldHandleCorrectly(SmaEventTimePeriod timePeriod)
    {
        // Arrange
        var symbol = "NVDA";

        // Act
        var filings = await _service.GetRecentFilingsAsync(symbol, timePeriod);

        // Assert
        filings.Should().NotBeNull();
        // Placeholder implementation returns empty results
        filings.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ShouldAvoidTradingAsync_WithValidSymbol_ShouldReturnDecision()
    {
        // Arrange
        var symbol = "GOOGL";

        // Act
        var recommendation = await _service.GetTradingRecommendationAsync(symbol, SmaEventFilterType.ExcludeEarnings);

        // Assert
        recommendation.Should().NotBeNull();
        recommendation.Symbol.Should().Be(symbol);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ShouldAvoidTradingAsync_WithNullSymbol_ShouldThrowArgumentException()
    {
        // Arrange
        string symbol = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _service.GetTradingRecommendationAsync(symbol, SmaEventFilterType.ExcludeEarnings));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ShouldAvoidTradingAsync_WithEmptySymbol_ShouldThrowArgumentException()
    {
        // Arrange
        var symbol = "";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _service.HasHighImpactEventsAsync(symbol, SmaEventTimePeriod.NextWeek));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetEventRiskScoreAsync_WithValidSymbol_ShouldReturnScore()
    {
        // Arrange
        var symbol = "META";

        // Act
        var impactLevel = await _service.GetEventImpactLevelAsync(symbol, DateTime.UtcNow);

        // Assert
        impactLevel.Should().BeOneOf(SmaEventImpactLevel.Low, SmaEventImpactLevel.Medium, SmaEventImpactLevel.High, SmaEventImpactLevel.Critical);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetEventRiskScoreAsync_WithNullSymbol_ShouldThrowArgumentException()
    {
        // Arrange
        string symbol = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _service.GetEventImpactLevelAsync(symbol, DateTime.UtcNow));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetEventRiskScoreAsync_WithEmptySymbol_ShouldThrowArgumentException()
    {
        // Arrange
        var symbol = "";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _service.GetEventImpactLevelAsync(symbol, DateTime.UtcNow));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetUpcomingEventsAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "AMZN";
        var timePeriod = SmaEventTimePeriod.NextWeek;

        // Act - Simulate concurrent calls (reduced from 5 to 2 for faster execution)
        var tasks = new List<Task<IEnumerable<MarketEvent>>>();
        for (int i = 0; i < 2; i++)
        {
            tasks.Add(_service.GetUpcomingEventsAsync(symbol, timePeriod));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(2);
        results.Should().OnlyContain(r => r != null);
        results.Should().OnlyContain(r => !r.Any()); // Placeholder returns empty
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFilingsAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "NFLX";
        var timePeriod = SmaEventTimePeriod.NextMonth;

        // Act - Simulate concurrent calls (reduced from 5 to 2 for faster execution)
        var tasks = new List<Task<IEnumerable<SmaTrendFollower.Models.SecFiling>>>();
        for (int i = 0; i < 2; i++)
        {
            tasks.Add(_service.GetRecentFilingsAsync(symbol, timePeriod));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(2);
        results.Should().OnlyContain(r => r != null);
        results.Should().OnlyContain(r => !r.Any()); // Placeholder returns empty
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ShouldAvoidTradingAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "UBER";

        // Act - Simulate concurrent calls
        var tasks = new List<Task<bool>>();
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(_service.HasHighImpactEventsAsync(symbol, SmaEventTimePeriod.NextWeek));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(10);
        results.Should().OnlyContain(r => r == false); // Placeholder returns false
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetEventRiskScoreAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "LYFT";

        // Act - Simulate concurrent calls
        var tasks = new List<Task<SmaEventImpactLevel>>();
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(_service.GetEventImpactLevelAsync(symbol, DateTime.UtcNow));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(10);
        results.Should().OnlyContain(r => r == SmaEventImpactLevel.Low); // Placeholder returns Low
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetUpcomingEventsAsync_WithCancellation_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.GetUpcomingEventsAsync("AAPL", SmaEventTimePeriod.NextWeek, cts.Token));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFilingsAsync_WithCancellation_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.GetRecentFilingsAsync("AAPL", SmaEventTimePeriod.NextWeek, cts.Token));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ShouldAvoidTradingAsync_WithCancellation_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.HasHighImpactEventsAsync("AAPL", SmaEventTimePeriod.NextWeek, cts.Token));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetEventRiskScoreAsync_WithCancellation_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.GetEventImpactLevelAsync("AAPL", DateTime.UtcNow, cts.Token));
    }
}
