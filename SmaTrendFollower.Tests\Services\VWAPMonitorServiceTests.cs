using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for VWAPMonitorService
/// Tests VWAP calculation, caching, and trend detection functionality
/// </summary>
public class VWAPMonitorServiceTests : IDisposable
{
    private readonly Mock<ITickStreamService> _mockTickStreamService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IMarketRegimeService> _mockMarketRegimeService;
    private readonly Mock<ILogger<VWAPMonitorService>> _mockLogger;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly VWAPMonitorService _vwapMonitorService;

    public VWAPMonitorServiceTests()
    {
        _mockTickStreamService = new Mock<ITickStreamService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockMarketRegimeService = new Mock<IMarketRegimeService>();
        _mockLogger = new Mock<ILogger<VWAPMonitorService>>();
        _mockDatabase = new Mock<IDatabase>();

        // Setup configuration using ConfigurationBuilder instead of mocking extension methods
        var configData = new Dictionary<string, string?>
        {
            ["VWAPMonitor:RollingMinutes"] = "30",
            ["VWAPMonitor:MinTradesRequired"] = "10",
            ["VWAPMonitor:MinVolumeThreshold"] = "1000",
            ["VWAPMonitor:RequireTrendingRegime"] = "true",
            ["VWAPMonitor:DeviationAlertThreshold"] = "2.0",
            ["VWAPMonitor:CacheExpiryMinutes"] = "5"
        };

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();

        // Setup Redis
        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(_mockDatabase.Object);

        _vwapMonitorService = new VWAPMonitorService(
            _mockTickStreamService.Object,
            _mockRedisService.Object,
            _mockMarketRegimeService.Object,
            configuration,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeSuccessfully()
    {
        // Act & Assert
        _vwapMonitorService.Should().NotBeNull();
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
        _vwapMonitorService.GetMonitoredSymbols().Should().BeEmpty();
    }

    [Fact]
    public void Constructor_WithNullTickStreamService_ShouldThrowArgumentNullException()
    {
        // Arrange
        var configData = new Dictionary<string, string?>
        {
            ["VWAPMonitor:RollingMinutes"] = "30"
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();

        // Act & Assert
        var act = () => new VWAPMonitorService(
            null!,
            _mockRedisService.Object,
            _mockMarketRegimeService.Object,
            configuration,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("tickStreamService");
    }

    [Fact]
    public async Task StartMonitoringAsync_WithValidSymbols_ShouldStartSuccessfully()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };

        // Act
        await _vwapMonitorService.StartMonitoringAsync(symbols);

        // Assert
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Active);
        _vwapMonitorService.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);

        // Verify tick stream subscription
        _mockTickStreamService.Verify(x => x.SubscribeAsync(
            It.Is<IEnumerable<string>>(s => s.SequenceEqual(symbols)),
            TickDataTypes.Trades | TickDataTypes.Aggregates,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task StartMonitoringAsync_WithEmptySymbols_ShouldNotStartMonitoring()
    {
        // Arrange
        var symbols = Array.Empty<string>();

        // Act
        await _vwapMonitorService.StartMonitoringAsync(symbols);

        // Assert
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
        _vwapMonitorService.GetMonitoredSymbols().Should().BeEmpty();

        // Verify no subscription
        _mockTickStreamService.Verify(x => x.SubscribeAsync(
            It.IsAny<IEnumerable<string>>(),
            It.IsAny<TickDataTypes>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task AddSymbolsAsync_WithNewSymbols_ShouldAddToMonitoring()
    {
        // Arrange
        var initialSymbols = new[] { "AAPL", "MSFT" };
        var newSymbols = new[] { "GOOGL", "TSLA" };
        
        await _vwapMonitorService.StartMonitoringAsync(initialSymbols);

        // Act
        await _vwapMonitorService.AddSymbolsAsync(newSymbols);

        // Assert
        var allSymbols = initialSymbols.Concat(newSymbols);
        _vwapMonitorService.GetMonitoredSymbols().Should().BeEquivalentTo(allSymbols);

        // Verify subscription for new symbols
        _mockTickStreamService.Verify(x => x.SubscribeAsync(
            It.Is<IEnumerable<string>>(s => s.SequenceEqual(newSymbols)),
            TickDataTypes.Trades | TickDataTypes.Aggregates,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RemoveSymbolsAsync_WithExistingSymbols_ShouldRemoveFromMonitoring()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var symbolsToRemove = new[] { "MSFT", "GOOGL" };
        
        await _vwapMonitorService.StartMonitoringAsync(symbols);

        // Act
        await _vwapMonitorService.RemoveSymbolsAsync(symbolsToRemove);

        // Assert
        _vwapMonitorService.GetMonitoredSymbols().Should().BeEquivalentTo(new[] { "AAPL" });
    }

    [Fact]
    public async Task GetCurrentVWAPAsync_WithCachedData_ShouldReturnCachedValue()
    {
        // Arrange
        var symbol = "AAPL";
        var cachedVWAP = new VWAPData(
            Symbol: symbol,
            VWAP: 150.50m,
            CurrentPrice: 151.00m,
            DeviationPercent: 0.33m,
            CumulativeVolume: 1000000,
            CumulativeValue: 150500000,
            Timestamp: DateTime.UtcNow,
            TradeCount: 500,
            Trend: VWAPTrend.AboveVWAP
        );

        var json = System.Text.Json.JsonSerializer.Serialize(cachedVWAP);
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(new RedisValue(json));

        // Act
        var result = await _vwapMonitorService.GetCurrentVWAPAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be(symbol);
        result.VWAP.Should().Be(150.50m);
        result.CurrentPrice.Should().Be(151.00m);
        result.Trend.Should().Be(VWAPTrend.AboveVWAP);
    }

    [Fact]
    public async Task IsPriceAboveVWAPAsync_WithTrendingRegimeRequired_ShouldCheckRegime()
    {
        // Arrange
        var symbol = "AAPL";
        var currentPrice = 151.00m;
        
        _mockMarketRegimeService.Setup(x => x.GetCachedRegimeAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(MarketRegime.TrendingUp);

        var vwapData = new VWAPData(
            Symbol: symbol,
            VWAP: 150.50m,
            CurrentPrice: currentPrice,
            DeviationPercent: 0.33m,
            CumulativeVolume: 1000000,
            CumulativeValue: 150500000,
            Timestamp: DateTime.UtcNow,
            TradeCount: 500,
            Trend: VWAPTrend.AboveVWAP
        );

        var json = System.Text.Json.JsonSerializer.Serialize(vwapData);
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(new RedisValue(json));

        // Act
        var result = await _vwapMonitorService.IsPriceAboveVWAPAsync(symbol, currentPrice);

        // Assert
        result.Should().BeTrue();
        _mockMarketRegimeService.Verify(x => x.GetCachedRegimeAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetVWAPDeviationAsync_WithValidData_ShouldCalculateCorrectDeviation()
    {
        // Arrange
        var symbol = "AAPL";
        var currentPrice = 151.50m;
        var vwap = 150.00m;
        var expectedDeviation = (currentPrice - vwap) / vwap * 100; // 1.0%

        var vwapData = new VWAPData(
            Symbol: symbol,
            VWAP: vwap,
            CurrentPrice: 150.00m, // This will be overridden by the calculation
            DeviationPercent: 0,
            CumulativeVolume: 1000000,
            CumulativeValue: 150000000,
            Timestamp: DateTime.UtcNow,
            TradeCount: 500,
            Trend: VWAPTrend.AboveVWAP
        );

        var json = System.Text.Json.JsonSerializer.Serialize(vwapData);
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(new RedisValue(json));

        // Act
        var result = await _vwapMonitorService.GetVWAPDeviationAsync(symbol, currentPrice);

        // Assert
        result.Should().NotBeNull();
        result!.Should().BeApproximately(expectedDeviation, 0.01m);
    }

    [Fact]
    public async Task StopMonitoringAsync_WhenActive_ShouldStopSuccessfully()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        await _vwapMonitorService.StartMonitoringAsync(symbols);

        // Act
        await _vwapMonitorService.StopMonitoringAsync();

        // Assert
        _vwapMonitorService.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
        _vwapMonitorService.GetMonitoredSymbols().Should().BeEmpty();
    }

    public void Dispose()
    {
        _vwapMonitorService?.Dispose();
    }
}
