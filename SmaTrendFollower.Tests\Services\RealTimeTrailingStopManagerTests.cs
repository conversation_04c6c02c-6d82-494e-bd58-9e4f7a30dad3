using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for RealTimeTrailingStopManager
/// Tests trailing stop logic, background service functionality, and real-time updates
/// </summary>
public class RealTimeTrailingStopManagerTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<ILogger<RealTimeTrailingStopManager>> _mockLogger;
    private readonly RealTimeTrailingStopManager _service;
    private readonly TrailingStopConfig _config;

    public RealTimeTrailingStopManagerTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockLogger = new Mock<ILogger<RealTimeTrailingStopManager>>();
        
        _config = new TrailingStopConfig(
            TrailingMethod: TrailingMethod.Hybrid,
            TrailingPercent: 0.05m,
            AtrMultiplier: 1.5m,
            MinimumStopMovementPercent: 0.005m,
            UpdateInterval: TimeSpan.FromSeconds(1)
        );

        _service = new RealTimeTrailingStopManager(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            _config
        );
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task AddTrailingStopAsync_ShouldCreateNewTrailingStop()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;

        _mockLiveStateStore
            .Setup(x => x.SetTrailingStopAsync(symbol, initialStopPrice, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockLiveStateStore
            .Setup(x => x.SetPositionStateAsync(symbol, It.IsAny<PositionState>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _service.AddTrailingStopAsync(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Assert
        var stopInfo = await _service.GetTrailingStopAsync(symbol);
        stopInfo.Should().NotBeNull();
        stopInfo!.Symbol.Should().Be(symbol);
        stopInfo.EntryPrice.Should().Be(entryPrice);
        stopInfo.CurrentStopPrice.Should().Be(initialStopPrice);
        stopInfo.Quantity.Should().Be(quantity);

        _mockLiveStateStore.Verify(
            x => x.SetTrailingStopAsync(symbol, initialStopPrice, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_WithHigherPrice_ShouldUpdateStop()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;
        var newPrice = 155.00m; // Higher price

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Act
        var update = await _service.UpdateTrailingStopAsync(symbol, newPrice);

        // Assert
        update.Should().NotBeNull();
        update.Symbol.Should().Be(symbol);
        update.ShouldUpdate.Should().BeTrue();
        update.NewStopPrice.Should().BeGreaterThan(initialStopPrice);
        update.CurrentPrice.Should().Be(newPrice);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_WithLowerPrice_ShouldNotUpdateStop()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;
        var lowerPrice = 148.00m; // Lower than entry

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Act
        var update = await _service.UpdateTrailingStopAsync(symbol, lowerPrice);

        // Assert
        update.Should().NotBeNull();
        update.ShouldUpdate.Should().BeFalse();
        update.Reason.Should().Contain("not higher than previous high");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_WithNonExistentSymbol_ShouldReturnNoActiveStop()
    {
        // Arrange
        var symbol = "NONEXISTENT";
        var currentPrice = 100.00m;

        // Act
        var update = await _service.UpdateTrailingStopAsync(symbol, currentPrice);

        // Assert
        update.Should().NotBeNull();
        update.ShouldUpdate.Should().BeFalse();
        update.Reason.Should().Contain("No active stop found");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task RemoveTrailingStopAsync_ShouldRemoveStopFromActiveList()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        _mockLiveStateStore
            .Setup(x => x.RemoveTrailingStopAsync(symbol, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _service.RemoveTrailingStopAsync(symbol);

        // Assert
        var stopInfo = await _service.GetTrailingStopAsync(symbol);
        stopInfo.Should().BeNull(); // Should be removed from active stops

        _mockLiveStateStore.Verify(
            x => x.RemoveTrailingStopAsync(symbol, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetTrailingStopAsync_WithActiveStop_ShouldReturnCorrectInfo()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Act
        var stopInfo = await _service.GetTrailingStopAsync(symbol);

        // Assert
        stopInfo.Should().NotBeNull();
        stopInfo!.Symbol.Should().Be(symbol);
        stopInfo.EntryPrice.Should().Be(entryPrice);
        stopInfo.CurrentStopPrice.Should().Be(initialStopPrice);
        stopInfo.HighestPrice.Should().Be(entryPrice); // Initially same as entry
        stopInfo.Quantity.Should().Be(quantity);
        stopInfo.RiskAmount.Should().Be((entryPrice - initialStopPrice) * quantity);
        stopInfo.UnrealizedPnL.Should().Be(0); // No movement yet
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetTrailingStopAsync_WithRedisRestore_ShouldRestoreFromRedis()
    {
        // Arrange
        var symbol = "AAPL";
        var stopPrice = 145.00m;
        var positionState = new PositionState(
            symbol, 150.00m, 152.00m, stopPrice, 100, DateTime.UtcNow.AddHours(-1), DateTime.UtcNow);

        _mockLiveStateStore
            .Setup(x => x.GetTrailingStopAsync(symbol, It.IsAny<CancellationToken>()))
            .ReturnsAsync(stopPrice);

        _mockLiveStateStore
            .Setup(x => x.GetPositionStateAsync(symbol, It.IsAny<CancellationToken>()))
            .ReturnsAsync(positionState);

        // Act
        var stopInfo = await _service.GetTrailingStopAsync(symbol);

        // Assert
        stopInfo.Should().NotBeNull();
        stopInfo!.Symbol.Should().Be(symbol);
        stopInfo.CurrentStopPrice.Should().Be(stopPrice);
        stopInfo.EntryPrice.Should().Be(positionState.EntryPrice);
        stopInfo.HighestPrice.Should().Be(positionState.CurrentPrice);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetAllTrailingStopsAsync_ShouldReturnAllActiveStops()
    {
        // Arrange
        await SetupTrailingStop("AAPL", 150.00m, 145.00m, 2.50m, 100);
        await SetupTrailingStop("MSFT", 200.00m, 190.00m, 3.00m, 50);
        await SetupTrailingStop("TSLA", 300.00m, 285.00m, 5.00m, 25);

        // Act
        var allStops = await _service.GetAllTrailingStopsAsync();

        // Assert
        allStops.Should().HaveCount(3);
        allStops.Should().Contain(s => s.Symbol == "AAPL");
        allStops.Should().Contain(s => s.Symbol == "MSFT");
        allStops.Should().Contain(s => s.Symbol == "TSLA");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Theory]
    [InlineData(TrailingMethod.FixedPercent, 150.00, 0.05, 142.50)] // 5% below 150
    [InlineData(TrailingMethod.AtrBased, 150.00, 1.5, 146.25)] // 150 - (2.5 * 1.5)
    [InlineData(TrailingMethod.Hybrid, 150.00, 1.5, 146.25)] // Max of both methods
    public async Task UpdateTrailingStopAsync_ShouldCalculateCorrectStopPrice(
        TrailingMethod method, decimal currentPrice, decimal multiplier, decimal expectedMinStop)
    {
        // Arrange
        var config = new TrailingStopConfig(
            TrailingMethod: method,
            TrailingPercent: 0.05m,
            AtrMultiplier: multiplier,
            MinimumStopMovementPercent: 0.001m // Very low to allow updates
        );

        var service = new RealTimeTrailingStopManager(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            config
        );

        var symbol = "TEST";
        var entryPrice = 140.00m;
        var initialStopPrice = 135.00m;
        var atrValue = 2.50m;

        await SetupTrailingStop(service, symbol, entryPrice, initialStopPrice, atrValue, 100);

        // Act
        var update = await service.UpdateTrailingStopAsync(symbol, currentPrice);

        // Assert
        if (method == TrailingMethod.Hybrid)
        {
            update.NewStopPrice.Should().BeGreaterOrEqualTo(expectedMinStop);
        }
        else
        {
            update.NewStopPrice.Should().BeApproximately(expectedMinStop, 0.01m);
        }

        service.Dispose();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_WithMinimumMovementThreshold_ShouldRespectThreshold()
    {
        // Arrange
        var config = new TrailingStopConfig(
            MinimumStopMovementPercent: 0.02m // 2% minimum movement
        );

        var service = new RealTimeTrailingStopManager(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            config
        );

        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var smallPriceIncrease = 150.50m; // Small increase

        await SetupTrailingStop(service, symbol, entryPrice, initialStopPrice, 2.50m, 100);

        // Act
        var update = await service.UpdateTrailingStopAsync(symbol, smallPriceIncrease);

        // Assert
        update.ShouldUpdate.Should().BeFalse();
        update.Reason.Should().Contain("below minimum");

        service.Dispose();
    }

    private async Task SetupTrailingStop(string symbol, decimal entryPrice, decimal initialStopPrice, 
        decimal atrValue, int quantity)
    {
        await SetupTrailingStop(_service, symbol, entryPrice, initialStopPrice, atrValue, quantity);
    }

    private async Task SetupTrailingStop(RealTimeTrailingStopManager service, string symbol, 
        decimal entryPrice, decimal initialStopPrice, decimal atrValue, int quantity)
    {
        _mockLiveStateStore
            .Setup(x => x.SetTrailingStopAsync(symbol, It.IsAny<decimal>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockLiveStateStore
            .Setup(x => x.SetPositionStateAsync(symbol, It.IsAny<PositionState>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        await service.AddTrailingStopAsync(symbol, entryPrice, initialStopPrice, atrValue, quantity);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task AddTrailingStopAsync_WithException_ShouldThrowAndLog()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;

        _mockLiveStateStore
            .Setup(x => x.SetTrailingStopAsync(symbol, initialStopPrice, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Redis connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() =>
            _service.AddTrailingStopAsync(symbol, entryPrice, initialStopPrice, atrValue, quantity));

        // Verify error was logged
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to add trailing stop")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_WithConcurrentAccess_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Act - Simulate concurrent updates (reduced from 10 to 3 for faster execution)
        var tasks = new List<Task<TrailingStopUpdate>>();
        for (int i = 0; i < 3; i++)
        {
            var price = 150.00m + i; // Increasing prices
            tasks.Add(_service.UpdateTrailingStopAsync(symbol, price));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().NotBeNull();
        results.Should().HaveCount(3);

        // At least some updates should have been successful
        results.Should().Contain(r => r.ShouldUpdate);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetTrailingStopAsync_WithNoRedisData_ShouldReturnNull()
    {
        // Arrange
        var symbol = "NONEXISTENT";

        _mockLiveStateStore
            .Setup(x => x.GetTrailingStopAsync(symbol, It.IsAny<CancellationToken>()))
            .ReturnsAsync((decimal?)null);

        _mockLiveStateStore
            .Setup(x => x.GetPositionStateAsync(symbol, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PositionState?)null);

        // Act
        var stopInfo = await _service.GetTrailingStopAsync(symbol);

        // Assert
        stopInfo.Should().BeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_ShouldUpdateHighestPrice()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;
        var higherPrice = 155.00m;

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Act
        await _service.UpdateTrailingStopAsync(symbol, higherPrice);
        var stopInfo = await _service.GetTrailingStopAsync(symbol);

        // Assert
        stopInfo.Should().NotBeNull();
        stopInfo!.HighestPrice.Should().Be(higherPrice);
        stopInfo.UnrealizedPnL.Should().Be((higherPrice - entryPrice) * quantity);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task UpdateTrailingStopAsync_ShouldPersistToRedis()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var initialStopPrice = 145.00m;
        var atrValue = 2.50m;
        var quantity = 100;
        var newPrice = 155.00m;

        await SetupTrailingStop(symbol, entryPrice, initialStopPrice, atrValue, quantity);

        // Act
        var update = await _service.UpdateTrailingStopAsync(symbol, newPrice);

        // Assert
        if (update.ShouldUpdate)
        {
            _mockLiveStateStore.Verify(
                x => x.SetTrailingStopAsync(symbol, update.NewStopPrice, It.IsAny<CancellationToken>()),
                Times.AtLeastOnce);

            _mockLiveStateStore.Verify(
                x => x.SetPositionStateAsync(symbol, It.IsAny<PositionState>(), It.IsAny<CancellationToken>()),
                Times.AtLeastOnce);
        }
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task GetAllTrailingStopsAsync_WithRedisRestore_ShouldIncludeRestoredStops()
    {
        // Arrange - Add one active stop
        await SetupTrailingStop("AAPL", 150.00m, 145.00m, 2.50m, 100);

        // Mock Redis data for another symbol
        var redisSymbol = "MSFT";
        var redisStopPrice = 190.00m;
        var redisPosition = new PositionState(
            redisSymbol, 200.00m, 205.00m, redisStopPrice, 50, DateTime.UtcNow.AddHours(-1), DateTime.UtcNow);

        _mockLiveStateStore
            .Setup(x => x.GetAllTrailingStopsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, decimal> { { redisSymbol, redisStopPrice } });

        _mockLiveStateStore
            .Setup(x => x.GetPositionStateAsync(redisSymbol, It.IsAny<CancellationToken>()))
            .ReturnsAsync(redisPosition);

        // Act
        var allStops = await _service.GetAllTrailingStopsAsync();

        // Assert
        allStops.Should().HaveCountGreaterOrEqualTo(1);
        allStops.Should().Contain(s => s.Symbol == "AAPL");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task TrailingStopCalculation_ShouldRespectConfiguredMethod()
    {
        // Arrange
        var fixedPercentConfig = new TrailingStopConfig(
            TrailingMethod: TrailingMethod.FixedPercent,
            TrailingPercent: 0.10m, // 10%
            MinimumStopMovementPercent: 0.001m
        );

        var service = new RealTimeTrailingStopManager(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            fixedPercentConfig
        );

        var symbol = "TEST";
        var entryPrice = 100.00m;
        var initialStopPrice = 90.00m;
        var currentPrice = 120.00m;

        await SetupTrailingStop(service, symbol, entryPrice, initialStopPrice, 2.0m, 100);

        // Act
        var update = await service.UpdateTrailingStopAsync(symbol, currentPrice);

        // Assert
        update.ShouldUpdate.Should().BeTrue();
        // For FixedPercent method: 120 * (1 - 0.10) = 108
        update.NewStopPrice.Should().BeApproximately(108.00m, 0.01m);

        service.Dispose();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    [Fact]
    public async Task RemoveTrailingStopAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var symbol = "AAPL";
        await SetupTrailingStop(symbol, 150.00m, 145.00m, 2.50m, 100);

        _mockLiveStateStore
            .Setup(x => x.RemoveTrailingStopAsync(symbol, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Redis error"));

        // Act & Assert - Should not throw
        await _service.RemoveTrailingStopAsync(symbol);

        // Verify error was logged
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to remove trailing stop")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
