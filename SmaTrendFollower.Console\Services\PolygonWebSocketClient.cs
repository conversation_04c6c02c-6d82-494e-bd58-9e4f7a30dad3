using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Console.Configuration;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Polygon WebSocket client for real-time streaming data
/// </summary>
public sealed class PolygonWebSocketClient : IPolygonWebSocketClient
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<PolygonWebSocketClient> _logger;
    private readonly string? _apiKey;
    
    private ClientWebSocket? _webSocket;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _receiveTask;
    private Task? _reconnectTask;
    private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);
    private readonly HashSet<string> _subscribedSymbols = new();
    private bool _disposed;
    private bool _shouldReconnect = true;
    private int _reconnectAttempts = 0;
    private const int MaxReconnectAttempts = 10;
    private readonly TimeSpan _baseReconnectDelay;
    private readonly TimeoutConfiguration _timeouts;

    // WebSocket URLs
    private const string DelayedStreamUrl = "wss://delayed.polygon.io/stocks";
    private const string RealTimeStreamUrl = "wss://socket.polygon.io/stocks";
    private const string IndicesStreamUrl = "wss://socket.polygon.io/indices";

    public PolygonWebSocketClient(IConfiguration configuration, ILogger<PolygonWebSocketClient> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _apiKey = _configuration["POLY_API_KEY"];
        ConnectionStatus = PolygonConnectionStatus.Disconnected;
    }

    // === Events ===

    public event EventHandler<PolygonConnectionStatusEventArgs>? ConnectionStatusChanged;
    public event EventHandler<PolygonIndexUpdateEventArgs>? IndexUpdated;
    public event EventHandler<PolygonTradeUpdateEventArgs>? TradeUpdated;
    public event EventHandler<PolygonQuoteUpdateEventArgs>? QuoteUpdated;
    public event EventHandler<PolygonAggregateUpdateEventArgs>? AggregateUpdated;
    public event EventHandler<PolygonErrorEventArgs>? ErrorOccurred;

    // === Properties ===
    
    public PolygonConnectionStatus ConnectionStatus { get; private set; }

    // === Connection Management ===

    public async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (ConnectionStatus == PolygonConnectionStatus.Connected || 
                ConnectionStatus == PolygonConnectionStatus.Connecting)
            {
                return;
            }

            if (string.IsNullOrEmpty(_apiKey))
            {
                throw new InvalidOperationException("POLY_API_KEY configuration is required for Polygon streaming");
            }

            SetConnectionStatus(PolygonConnectionStatus.Connecting, "Connecting to Polygon WebSocket...");
            _logger.LogInformation("Connecting to Polygon WebSocket stream...");

            _cancellationTokenSource = new CancellationTokenSource();
            _webSocket = new ClientWebSocket();

            // Use real-time stream URL (requires paid subscription)
            var uri = new Uri(RealTimeStreamUrl);
            await _webSocket.ConnectAsync(uri, cancellationToken);

            SetConnectionStatus(PolygonConnectionStatus.Connected, "Connected to Polygon WebSocket");
            _logger.LogInformation("Successfully connected to Polygon WebSocket");

            // Start receiving messages
            _receiveTask = ReceiveMessagesAsync(_cancellationTokenSource.Token);

            // Authenticate
            await AuthenticateAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            SetConnectionStatus(PolygonConnectionStatus.Error, "Failed to connect", ex);
            _logger.LogError(ex, "Failed to connect to Polygon WebSocket");
            throw;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (ConnectionStatus == PolygonConnectionStatus.Disconnected)
            {
                return;
            }

            _logger.LogInformation("Disconnecting from Polygon WebSocket...");

            // Stop automatic reconnection
            _shouldReconnect = false;
            _cancellationTokenSource?.Cancel();

            if (_webSocket?.State == WebSocketState.Open)
            {
                await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Client disconnect", cancellationToken);
            }

            _webSocket?.Dispose();
            _webSocket = null;

            // Wait for tasks to complete
            if (_receiveTask != null)
            {
                try
                {
                    await _receiveTask;
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
            }

            if (_reconnectTask != null)
            {
                try
                {
                    await _reconnectTask;
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
            }

            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            _receiveTask = null;
            _reconnectTask = null;

            _subscribedSymbols.Clear();
            SetConnectionStatus(PolygonConnectionStatus.Disconnected, "Disconnected from Polygon WebSocket");
            _logger.LogInformation("Disconnected from Polygon WebSocket");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Polygon WebSocket disconnect");
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    // === Subscription Management ===

    public async Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            throw new InvalidOperationException("Must be connected and authenticated before subscribing");
        }

        var symbols = indexSymbols.ToList();
        _logger.LogInformation("Subscribing to index updates for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols));

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"V.{s}"))
        };

        await SendMessageAsync(subscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Add(symbol);
        }
    }

    public async Task SubscribeToTradeUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            throw new InvalidOperationException("Must be connected and authenticated before subscribing");
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to trade updates for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols));

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"T.{s}"))
        };

        await SendMessageAsync(subscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Add(symbol);
        }
    }

    public async Task SubscribeToQuoteUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            throw new InvalidOperationException("Must be connected and authenticated before subscribing");
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to quote updates for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols));

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"Q.{s}"))
        };

        await SendMessageAsync(subscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Add(symbol);
        }
    }

    public async Task SubscribeToAggregateUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            throw new InvalidOperationException("Must be connected and authenticated before subscribing");
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to aggregate updates for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols));

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"A.{s}"))
        };

        await SendMessageAsync(subscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Add(symbol);
        }
    }

    public async Task UnsubscribeFromIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            return; // Nothing to unsubscribe from
        }

        var symbols = indexSymbols.ToList();
        _logger.LogInformation("Unsubscribing from index updates for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols));

        var unsubscribeMessage = new
        {
            action = "unsubscribe",
            @params = string.Join(",", symbols.Select(s => $"V.{s}"))
        };

        await SendMessageAsync(unsubscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Remove(symbol);
        }
    }

    public async Task SubscribeToEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            throw new InvalidOperationException("Must be connected and authenticated before subscribing");
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to equity updates (trades and quotes) for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols.Take(10)));

        // Subscribe to both trades and quotes for comprehensive equity data
        var tradeParams = string.Join(",", symbols.Select(s => $"T.{s}"));
        var quoteParams = string.Join(",", symbols.Select(s => $"Q.{s}"));

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = $"{tradeParams},{quoteParams}"
        };

        await SendMessageAsync(subscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Add(symbol);
        }
    }

    public async Task UnsubscribeFromEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (ConnectionStatus != PolygonConnectionStatus.Authenticated)
        {
            return; // Nothing to unsubscribe from
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Unsubscribing from equity updates for {Count} symbols: {Symbols}",
            symbols.Count, string.Join(", ", symbols.Take(10)));

        // Unsubscribe from both trades and quotes
        var tradeParams = string.Join(",", symbols.Select(s => $"T.{s}"));
        var quoteParams = string.Join(",", symbols.Select(s => $"Q.{s}"));

        var unsubscribeMessage = new
        {
            action = "unsubscribe",
            @params = $"{tradeParams},{quoteParams}"
        };

        await SendMessageAsync(unsubscribeMessage, cancellationToken);

        foreach (var symbol in symbols)
        {
            _subscribedSymbols.Remove(symbol);
        }
    }

    public async Task UnsubscribeAllAsync(CancellationToken cancellationToken = default)
    {
        if (_subscribedSymbols.Any())
        {
            await UnsubscribeFromIndexUpdatesAsync(_subscribedSymbols.ToList(), cancellationToken);
        }
    }

    // === Private Methods ===

    private async Task AuthenticateAsync(CancellationToken cancellationToken)
    {
        SetConnectionStatus(PolygonConnectionStatus.Authenticating, "Authenticating...");
        _logger.LogInformation("Authenticating with Polygon WebSocket...");

        var authMessage = new
        {
            action = "auth",
            @params = _apiKey
        };

        await SendMessageAsync(authMessage, cancellationToken);
    }

    private async Task SendMessageAsync(object message, CancellationToken cancellationToken)
    {
        if (_webSocket?.State != WebSocketState.Open)
        {
            throw new InvalidOperationException("WebSocket is not connected");
        }

        var json = JsonSerializer.Serialize(message);
        var bytes = Encoding.UTF8.GetBytes(json);
        var buffer = new ArraySegment<byte>(bytes);

        _logger.LogDebug("Sending message to Polygon WebSocket: {Message}", json);
        await _webSocket.SendAsync(buffer, WebSocketMessageType.Text, true, cancellationToken);
    }

    private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
    {
        var buffer = new byte[4096];

        try
        {
            while (!cancellationToken.IsCancellationRequested && _webSocket?.State == WebSocketState.Open)
            {
                var result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await ProcessMessageAsync(message);
                }
                else if (result.MessageType == WebSocketMessageType.Close)
                {
                    _logger.LogInformation("Polygon WebSocket closed by server");
                    break;
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error receiving messages from Polygon WebSocket");
            SetConnectionStatus(PolygonConnectionStatus.Error, "Error receiving messages", ex);

            // Trigger reconnection if enabled
            if (_shouldReconnect && !cancellationToken.IsCancellationRequested)
            {
                _ = Task.Run(() => ReconnectAsync(cancellationToken), cancellationToken);
            }
        }
    }

    private async Task ProcessMessageAsync(string message)
    {
        try
        {
            _logger.LogDebug("Received message from Polygon WebSocket: {Message}", message);

            // Polygon sends messages as JSON arrays
            using var document = JsonDocument.Parse(message);
            if (document.RootElement.ValueKind == JsonValueKind.Array)
            {
                foreach (var element in document.RootElement.EnumerateArray())
                {
                    await ProcessSingleMessageAsync(element);
                }
            }
            else
            {
                await ProcessSingleMessageAsync(document.RootElement);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message from Polygon WebSocket: {Message}", message);
            OnErrorOccurred(new PolygonErrorEventArgs
            {
                Message = "Error processing WebSocket message",
                Exception = ex
            });
        }
    }

    private async Task ProcessSingleMessageAsync(JsonElement element)
    {
        await Task.CompletedTask; // Make async for future expansion

        if (!element.TryGetProperty("ev", out var eventTypeElement))
        {
            return;
        }

        var eventType = eventTypeElement.GetString();

        switch (eventType)
        {
            case "status":
                ProcessStatusMessage(element);
                break;
            case "V": // Index value update
                ProcessIndexValueMessage(element);
                break;
            case "T": // Trade update
                ProcessTradeMessage(element);
                break;
            case "Q": // Quote update
                ProcessQuoteMessage(element);
                break;
            case "A": // Aggregate update
                ProcessAggregateMessage(element);
                break;
            default:
                _logger.LogDebug("Received unknown message type: {EventType}", eventType);
                break;
        }
    }

    private void ProcessStatusMessage(JsonElement element)
    {
        var status = element.TryGetProperty("status", out var statusProp) ? statusProp.GetString() : "";
        var message = element.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : "";

        _logger.LogInformation("Polygon status: {Status} - {Message}", status, message);

        if (status == "connected")
        {
            SetConnectionStatus(PolygonConnectionStatus.Connected, message);
        }
        else if (status == "auth_success")
        {
            SetConnectionStatus(PolygonConnectionStatus.Authenticated, message);
            _logger.LogInformation("Successfully authenticated with Polygon WebSocket");
        }
        else if (status == "auth_failed")
        {
            SetConnectionStatus(PolygonConnectionStatus.Error, $"Authentication failed: {message}");
            OnErrorOccurred(new PolygonErrorEventArgs
            {
                Message = $"Authentication failed: {message}"
            });
        }
    }

    private void ProcessIndexValueMessage(JsonElement element)
    {
        try
        {
            var symbol = element.TryGetProperty("sym", out var symProp) ? symProp.GetString() ?? "" : "";
            var value = element.TryGetProperty("val", out var valProp) ? valProp.GetDecimal() : 0m;
            var timestamp = element.TryGetProperty("t", out var tProp) ? tProp.GetInt64() : 0;
            
            // Optional fields
            var change = element.TryGetProperty("c", out var cProp) ? cProp.GetDecimal() : 0m;
            var changePercent = element.TryGetProperty("cp", out var cpProp) ? cpProp.GetDecimal() : 0m;
            var volume = element.TryGetProperty("v", out var vProp) ? vProp.GetInt64() : 0;

            var eventArgs = new PolygonIndexUpdateEventArgs
            {
                IndexSymbol = symbol,
                Value = value,
                Change = change,
                ChangePercent = changePercent,
                Volume = volume,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime
            };

            OnIndexUpdated(eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing index value message");
        }
    }

    private void ProcessTradeMessage(JsonElement element)
    {
        try
        {
            var symbol = element.TryGetProperty("sym", out var symProp) ? symProp.GetString() ?? "" : "";
            var price = element.TryGetProperty("p", out var pProp) ? pProp.GetDecimal() : 0m;
            var size = element.TryGetProperty("s", out var sProp) ? sProp.GetInt64() : 0;
            var timestamp = element.TryGetProperty("t", out var tProp) ? tProp.GetInt64() : 0;
            var exchange = element.TryGetProperty("x", out var xProp) ? xProp.GetString() ?? "" : "";
            var conditions = element.TryGetProperty("c", out var cProp) ? string.Join(",", cProp.EnumerateArray().Select(c => c.GetString())) : "";

            var eventArgs = new PolygonTradeUpdateEventArgs
            {
                Symbol = symbol,
                Price = price,
                Size = size,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime,
                Exchange = exchange,
                Conditions = conditions
            };

            OnTradeUpdated(eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trade message");
        }
    }

    private void ProcessQuoteMessage(JsonElement element)
    {
        try
        {
            var symbol = element.TryGetProperty("sym", out var symProp) ? symProp.GetString() ?? "" : "";
            var bidPrice = element.TryGetProperty("bp", out var bpProp) ? bpProp.GetDecimal() : 0m;
            var askPrice = element.TryGetProperty("ap", out var apProp) ? apProp.GetDecimal() : 0m;
            var bidSize = element.TryGetProperty("bs", out var bsProp) ? bsProp.GetInt64() : 0;
            var askSize = element.TryGetProperty("as", out var asProp) ? asProp.GetInt64() : 0;
            var timestamp = element.TryGetProperty("t", out var tProp) ? tProp.GetInt64() : 0;
            var exchange = element.TryGetProperty("x", out var xProp) ? xProp.GetString() ?? "" : "";

            var eventArgs = new PolygonQuoteUpdateEventArgs
            {
                Symbol = symbol,
                BidPrice = bidPrice,
                AskPrice = askPrice,
                BidSize = bidSize,
                AskSize = askSize,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime,
                Exchange = exchange
            };

            OnQuoteUpdated(eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing quote message");
        }
    }

    private void ProcessAggregateMessage(JsonElement element)
    {
        try
        {
            var symbol = element.TryGetProperty("sym", out var symProp) ? symProp.GetString() ?? "" : "";
            var open = element.TryGetProperty("o", out var oProp) ? oProp.GetDecimal() : 0m;
            var high = element.TryGetProperty("h", out var hProp) ? hProp.GetDecimal() : 0m;
            var low = element.TryGetProperty("l", out var lProp) ? lProp.GetDecimal() : 0m;
            var close = element.TryGetProperty("c", out var cProp) ? cProp.GetDecimal() : 0m;
            var volume = element.TryGetProperty("v", out var vProp) ? vProp.GetInt64() : 0;
            var vwap = element.TryGetProperty("vw", out var vwProp) ? vwProp.GetDecimal() : 0m;
            var timestamp = element.TryGetProperty("t", out var tProp) ? tProp.GetInt64() : 0;
            var tradeCount = element.TryGetProperty("n", out var nProp) ? nProp.GetInt64() : 0;

            var eventArgs = new PolygonAggregateUpdateEventArgs
            {
                Symbol = symbol,
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                Vwap = vwap,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime,
                TradeCount = tradeCount
            };

            OnAggregateUpdated(eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing aggregate message");
        }
    }

    private void SetConnectionStatus(PolygonConnectionStatus status, string? message = null, Exception? exception = null)
    {
        ConnectionStatus = status;
        OnConnectionStatusChanged(new PolygonConnectionStatusEventArgs
        {
            Status = status,
            Message = message,
            Exception = exception
        });
    }

    private void OnConnectionStatusChanged(PolygonConnectionStatusEventArgs e)
    {
        ConnectionStatusChanged?.Invoke(this, e);
    }

    private void OnIndexUpdated(PolygonIndexUpdateEventArgs e)
    {
        IndexUpdated?.Invoke(this, e);
    }

    private void OnTradeUpdated(PolygonTradeUpdateEventArgs e)
    {
        TradeUpdated?.Invoke(this, e);
    }

    private void OnQuoteUpdated(PolygonQuoteUpdateEventArgs e)
    {
        QuoteUpdated?.Invoke(this, e);
    }

    private void OnAggregateUpdated(PolygonAggregateUpdateEventArgs e)
    {
        AggregateUpdated?.Invoke(this, e);
    }

    private void OnErrorOccurred(PolygonErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    private async Task ReconnectAsync(CancellationToken cancellationToken)
    {
        if (!_shouldReconnect || _reconnectAttempts >= MaxReconnectAttempts)
        {
            _logger.LogWarning("Maximum reconnection attempts reached or reconnection disabled");
            return;
        }

        _reconnectAttempts++;
        var delay = TimeSpan.FromMilliseconds(_baseReconnectDelay.TotalMilliseconds * Math.Pow(2, _reconnectAttempts - 1));
        delay = delay.Add(TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000))); // Add jitter

        _logger.LogInformation("Attempting to reconnect to Polygon WebSocket (attempt {Attempt}/{MaxAttempts}) in {Delay}ms",
            _reconnectAttempts, MaxReconnectAttempts, delay.TotalMilliseconds);

        SetConnectionStatus(PolygonConnectionStatus.Reconnecting, $"Reconnecting (attempt {_reconnectAttempts})");

        try
        {
            await Task.Delay(delay, cancellationToken);

            if (cancellationToken.IsCancellationRequested || !_shouldReconnect)
            {
                return;
            }

            // Store subscribed symbols for re-subscription
            var symbolsToResubscribe = _subscribedSymbols.ToList();

            // Attempt reconnection
            await ConnectAsync(cancellationToken);

            // Re-subscribe to previous symbols
            if (symbolsToResubscribe.Any())
            {
                await SubscribeToIndexUpdatesAsync(symbolsToResubscribe, cancellationToken);
            }

            // Reset reconnection attempts on successful connection
            _reconnectAttempts = 0;
            _logger.LogInformation("Successfully reconnected to Polygon WebSocket");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reconnect to Polygon WebSocket (attempt {Attempt})", _reconnectAttempts);

            if (_reconnectAttempts < MaxReconnectAttempts && _shouldReconnect)
            {
                // Schedule next reconnection attempt
                _ = Task.Run(() => ReconnectAsync(cancellationToken), cancellationToken);
            }
            else
            {
                _logger.LogError("All reconnection attempts failed. Manual reconnection required.");
                SetConnectionStatus(PolygonConnectionStatus.Error, "All reconnection attempts failed");
            }
        }
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (!_disposed)
        {
            DisconnectAsync().GetAwaiter().GetResult();
            _connectionSemaphore?.Dispose();
            _disposed = true;
        }
    }
}
