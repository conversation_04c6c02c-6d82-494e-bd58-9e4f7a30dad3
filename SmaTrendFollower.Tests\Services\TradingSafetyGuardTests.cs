using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

[Collection("EnvironmentVariableTests")]
public class TradingSafetyGuardTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<IAlpacaRateLimitHelper> _mockRateLimitHelper;
    private readonly Mock<ILogger<TradingSafetyGuard>> _mockLogger;
    private readonly TradingSafetyGuard _safetyGuard;

    public TradingSafetyGuardTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();
        _mockLogger = new Mock<ILogger<TradingSafetyGuard>>();

        _mockClientFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockClientFactory.Setup(x => x.GetRateLimitHelper()).Returns(_mockRateLimitHelper.Object);

        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<SafetyCheckResult>(It.IsAny<Func<Task<SafetyCheckResult>>>(), It.IsAny<string>()))
            .Returns<Func<Task<SafetyCheckResult>>, string>((func, name) => func());

        _safetyGuard = new TradingSafetyGuard(_mockClientFactory.Object, _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithDryRunMode_ShouldBlockTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration { DryRunMode = true };
        _safetyGuard.UpdateConfiguration(config);

        // Act
        var result = await _safetyGuard.ValidateTradeAsync(signal, 10m);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.Reason.Should().Contain("Dry run mode");
        result.Level.Should().Be(SafetyLevel.Info);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithPaperOnlyAndLiveEnvironment_ShouldBlockTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration
        {
            AllowedEnvironment = TradingEnvironment.Paper,
            MaxPositionSizePercent = 0.20m // Set higher to avoid position size check failing first
        };
        _safetyGuard.UpdateConfiguration(config);

        // Set environment to simulate live trading
        Environment.SetEnvironmentVariable("APCA_API_ENV", "live");

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        _mockTradingClient.Setup(x => x.ListPositionsAsync(default))
            .ReturnsAsync(new List<IPosition>());

        try
        {
            // Act
            var result = await _safetyGuard.ValidateTradeAsync(signal, 10m);

            // Assert
            result.IsAllowed.Should().BeFalse();
            result.Reason.Should().Contain("Live trading not allowed");
            result.Level.Should().Be(SafetyLevel.Critical);
        }
        finally
        {
            // Clean up environment variable
            Environment.SetEnvironmentVariable("APCA_API_ENV", null);
        }
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithInsufficientEquity_ShouldBlockTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration { MinAccountEquity = 5000m, AllowedEnvironment = TradingEnvironment.Both };
        _safetyGuard.UpdateConfiguration(config);

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(1000m); // Below minimum

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var result = await _safetyGuard.ValidateTradeAsync(signal, 10m);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.Reason.Should().Contain("Account equity");
        result.Reason.Should().Contain("below minimum");
        result.Level.Should().Be(SafetyLevel.Critical);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithTooManyPositions_ShouldBlockTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration { MaxPositions = 2, AllowedEnvironment = TradingEnvironment.Both };
        _safetyGuard.UpdateConfiguration(config);

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m);

        var mockPosition1 = new Mock<IPosition>();
        mockPosition1.Setup(x => x.Quantity).Returns(10);
        var mockPosition2 = new Mock<IPosition>();
        mockPosition2.Setup(x => x.Quantity).Returns(5);
        var mockPosition3 = new Mock<IPosition>();
        mockPosition3.Setup(x => x.Quantity).Returns(8);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        _mockTradingClient.Setup(x => x.ListPositionsAsync(default))
            .ReturnsAsync(new List<IPosition> { mockPosition1.Object, mockPosition2.Object, mockPosition3.Object });

        // Act
        var result = await _safetyGuard.ValidateTradeAsync(signal, 10m);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.Reason.Should().Contain("Maximum positions reached");
        result.Level.Should().Be(SafetyLevel.Warning);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithExcessiveTradeValue_ShouldBlockTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration { MaxSingleTradeValue = 1000m, AllowedEnvironment = TradingEnvironment.Both };
        _safetyGuard.UpdateConfiguration(config);

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        _mockTradingClient.Setup(x => x.ListPositionsAsync(default))
            .ReturnsAsync(new List<IPosition>());

        // Act - Trade value = 150 * 10 = 1500, which exceeds 1000 limit
        var result = await _safetyGuard.ValidateTradeAsync(signal, 10m);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.Reason.Should().Contain("Trade value");
        result.Reason.Should().Contain("exceeds maximum");
        result.Level.Should().Be(SafetyLevel.Warning);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithExcessivePositionSize_ShouldBlockTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration { MaxPositionSizePercent = 0.05m, AllowedEnvironment = TradingEnvironment.Both };
        _safetyGuard.UpdateConfiguration(config);

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m); // 5% = $500 max position

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        _mockTradingClient.Setup(x => x.ListPositionsAsync(default))
            .ReturnsAsync(new List<IPosition>());

        // Act - Position value = 150 * 10 = 1500, which is 15% of 10000 equity
        var result = await _safetyGuard.ValidateTradeAsync(signal, 10m);

        // Assert
        result.IsAllowed.Should().BeFalse();
        result.Reason.Should().Contain("Position size");
        result.Reason.Should().Contain("exceeds maximum");
        result.Level.Should().Be(SafetyLevel.Warning);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradeAsync_WithValidConditions_ShouldAllowTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var config = new SafetyConfiguration 
        { 
            AllowedEnvironment = TradingEnvironment.Both,
            RequireConfirmation = false,
            MaxSingleTradeValue = 2000m,
            MaxPositionSizePercent = 0.20m,
            MinAccountEquity = 1000m
        };
        _safetyGuard.UpdateConfiguration(config);

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        _mockTradingClient.Setup(x => x.ListPositionsAsync(default))
            .ReturnsAsync(new List<IPosition>());

        // Act
        var result = await _safetyGuard.ValidateTradeAsync(signal, 5m); // Trade value = 750

        // Assert
        result.IsAllowed.Should().BeTrue();
        result.Reason.Should().Contain("All safety checks passed");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ValidateTradingCycleAsync_WithValidConditions_ShouldAllowTrading()
    {
        // Arrange
        var config = new SafetyConfiguration { AllowedEnvironment = TradingEnvironment.Both };
        _safetyGuard.UpdateConfiguration(config);

        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var result = await _safetyGuard.ValidateTradingCycleAsync();

        // Assert
        result.IsAllowed.Should().BeTrue();
        result.Reason.Should().Contain("Trading cycle validation passed");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void UpdateConfiguration_ShouldUpdateSettings()
    {
        // Arrange
        var newConfig = new SafetyConfiguration
        {
            MaxDailyLoss = 1000m,
            DryRunMode = true
        };

        // Act
        _safetyGuard.UpdateConfiguration(newConfig);
        var retrievedConfig = _safetyGuard.GetConfiguration();

        // Assert
        retrievedConfig.MaxDailyLoss.Should().Be(1000m);
        retrievedConfig.DryRunMode.Should().BeTrue();
    }
}
