using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced synthetic VIX service with machine learning-based regression weights
/// Provides robust VIX estimation when primary data sources fail
/// Uses weekly-trained regression coefficients for optimal accuracy
/// </summary>
public interface ISyntheticVixService
{
    /// <summary>
    /// Estimates VIX using trained regression weights from ETF proxies
    /// Falls back to static coefficients if training data unavailable
    /// </summary>
    Task<decimal?> EstimateAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the current regression weights used for estimation
    /// </summary>
    Task<SyntheticVixWeights?> GetCurrentWeightsAsync();
    
    /// <summary>
    /// Validates that regression weights are fresh and reliable
    /// </summary>
    Task<bool> AreWeightsFreshAsync();
}

/// <summary>
/// Production-ready synthetic VIX service with ML-trained regression weights
/// Enforces 15-minute data freshness requirement for all ETF inputs
/// </summary>
public sealed class SyntheticVixService : ISyntheticVixService, IDisposable
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IDatabase _redis;
    private readonly IConfiguration _configuration;
    private readonly ILogger<SyntheticVixService> _logger;
    private string? _polygonApiKey;
    private readonly SemaphoreSlim _estimationLock = new(1, 1);
    private bool _disposed;

    // Static fallback coefficients (used when trained weights unavailable)
    private static readonly SyntheticVixWeights StaticFallbackWeights = new()
    {
        VxxCoefficient = 0.7m,
        UvxyCoefficient = 0.3m,
        SvxyCoefficient = 0.0m,
        SpyCoefficient = 0.0m,
        Intercept = 0.0m,
        TrainedAt = DateTime.MinValue,
        RSquared = 0.65m,
        SampleSize = 0
    };

    public SyntheticVixService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ConnectionMultiplexer connectionMultiplexer,
        ILogger<SyntheticVixService> logger)
    {
        _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        _redis = connectionMultiplexer?.GetDatabase() ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    private string GetPolygonApiKey()
    {
        if (_polygonApiKey == null)
        {
            _polygonApiKey = _configuration["POLYGON_API_KEY"]
                ?? _configuration["POLY_API_KEY"]
                ?? Environment.GetEnvironmentVariable("POLYGON_API_KEY")
                ?? Environment.GetEnvironmentVariable("POLY_API_KEY")
                ?? throw new InvalidOperationException("POLYGON_API_KEY configuration missing. Please ensure POLYGON_API_KEY is set in .env file or environment variables.");

            _logger.LogDebug("Polygon API key loaded successfully");
        }
        return _polygonApiKey;
    }

    public async Task<decimal?> EstimateAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SyntheticVixService));

        await _estimationLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("Starting synthetic VIX estimation using trained regression weights");

            // Get current regression weights (trained or fallback)
            var weights = await GetCurrentWeightsAsync();
            if (weights == null)
            {
                _logger.LogWarning("No regression weights available, using static fallback");
                weights = StaticFallbackWeights;
            }

            // Fetch fresh ETF prices with data freshness validation
            var etfPrices = await FetchFreshEtfPricesAsync(cancellationToken);
            if (etfPrices == null)
            {
                _logger.LogError("Failed to fetch fresh ETF prices for synthetic VIX calculation");
                return null;
            }

            // Calculate synthetic VIX using regression formula
            var syntheticVix = CalculateSyntheticVix(weights, etfPrices);

            // Validate result is within reasonable bounds
            if (syntheticVix < 8m || syntheticVix > 80m)
            {
                _logger.LogWarning("Synthetic VIX out of bounds: {VIX:F2}, rejecting estimate", syntheticVix);
                return null;
            }

            // Cache the result with appropriate TTL
            await CacheSyntheticVixAsync(syntheticVix, weights, etfPrices);

            _logger.LogInformation("Synthetic VIX estimated: {VIX:F2} (R²={RSquared:F3}, trained: {TrainedAt:yyyy-MM-dd})",
                syntheticVix, weights.RSquared, weights.TrainedAt);

            return syntheticVix;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating synthetic VIX");
            return null;
        }
        finally
        {
            _estimationLock.Release();
        }
    }

    public async Task<SyntheticVixWeights?> GetCurrentWeightsAsync()
    {
        try
        {
            var weightsJson = await _redis.StringGetAsync("vix:weights");
            if (!weightsJson.HasValue)
            {
                _logger.LogDebug("No trained weights found in Redis, will use static fallback");
                return null;
            }

            var weights = JsonSerializer.Deserialize<SyntheticVixWeights>(weightsJson!);
            
            // Validate weights are not too old (max 14 days)
            if (weights != null && DateTime.UtcNow - weights.TrainedAt > TimeSpan.FromDays(14))
            {
                _logger.LogWarning("Regression weights are stale (age: {Age}), should retrain soon",
                    DateTime.UtcNow - weights.TrainedAt);
            }

            return weights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving regression weights from Redis");
            return null;
        }
    }

    public async Task<bool> AreWeightsFreshAsync()
    {
        var weights = await GetCurrentWeightsAsync();
        if (weights == null) return false;

        var age = DateTime.UtcNow - weights.TrainedAt;
        return age <= TimeSpan.FromDays(7); // Fresh if trained within last week
    }

    private async Task<EtfPriceSnapshot?> FetchFreshEtfPricesAsync(CancellationToken cancellationToken)
    {
        var symbols = new[] { "VXX", "UVXY", "SVXY", "SPY" };
        var prices = new Dictionary<string, decimal>();
        var timestamps = new Dictionary<string, DateTime>();

        var httpClient = _httpClientFactory.CreateClient("Polygon");

        foreach (var symbol in symbols)
        {
            try
            {
                var price = await GetFreshEtfPriceAsync(httpClient, symbol, cancellationToken);
                if (price.HasValue)
                {
                    prices[symbol] = price.Value.Price;
                    timestamps[symbol] = price.Value.Timestamp;
                }
                else
                {
                    _logger.LogWarning("Failed to get fresh price for {Symbol}", symbol);
                    return null; // Require all ETF prices for reliable estimation
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching price for {Symbol}", symbol);
                return null;
            }
        }

        return new EtfPriceSnapshot
        {
            VxxPrice = prices["VXX"],
            UvxyPrice = prices["UVXY"],
            SvxyPrice = prices["SVXY"],
            SpyPrice = prices["SPY"],
            Timestamp = timestamps.Values.Min(), // Use oldest timestamp for conservative freshness
            DataAge = DateTime.UtcNow - timestamps.Values.Min()
        };
    }

    private async Task<(decimal Price, DateTime Timestamp)?> GetFreshEtfPriceAsync(
        HttpClient httpClient, string symbol, CancellationToken cancellationToken)
    {
        try
        {
            // Use Polygon last trade endpoint for real-time pricing
            var url = $"/v2/last/trade/{symbol}?apiKey={GetPolygonApiKey()}";
            var response = await httpClient.GetAsync(url, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogDebug("Polygon API returned {StatusCode} for {Symbol}", response.StatusCode, symbol);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var jsonDoc = JsonDocument.Parse(content);

            if (jsonDoc.RootElement.TryGetProperty("results", out var results))
            {
                if (results.TryGetProperty("p", out var priceElement) &&
                    results.TryGetProperty("t", out var timestampElement))
                {
                    var price = priceElement.GetDecimal();
                    var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).UtcDateTime;
                    
                    // Enforce 15-minute freshness requirement
                    var dataAge = DateTime.UtcNow - timestamp;
                    if (dataAge > TimeSpan.FromMinutes(15))
                    {
                        _logger.LogWarning("ETF {Symbol} data is stale (age: {Age}), rejecting for synthetic VIX",
                            symbol, dataAge);
                        return null;
                    }

                    return (price, timestamp);
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching fresh price for {Symbol}", symbol);
            return null;
        }
    }

    private static decimal CalculateSyntheticVix(SyntheticVixWeights weights, EtfPriceSnapshot prices)
    {
        // Linear regression formula: VIX = a*VXX + b*UVXY + c*SVXY + d*SPY + e
        return weights.VxxCoefficient * prices.VxxPrice +
               weights.UvxyCoefficient * prices.UvxyPrice +
               weights.SvxyCoefficient * prices.SvxyPrice +
               weights.SpyCoefficient * prices.SpyPrice +
               weights.Intercept;
    }

    private async Task CacheSyntheticVixAsync(decimal vixValue, SyntheticVixWeights weights, EtfPriceSnapshot prices)
    {
        try
        {
            // Cache the synthetic VIX value
            await _redis.StringSetAsync("vix:synthetic", (double)vixValue, TimeSpan.FromMinutes(10));
            await _redis.StringSetAsync("vix:source", "synthetic", TimeSpan.FromMinutes(10));

            // Cache metadata for debugging
            var metadata = new
            {
                Value = vixValue,
                Weights = weights,
                Prices = prices,
                CalculatedAt = DateTime.UtcNow
            };

            await _redis.StringSetAsync("vix:synthetic:metadata", 
                JsonSerializer.Serialize(metadata), TimeSpan.FromMinutes(30));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching synthetic VIX data");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _estimationLock?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Regression weights for synthetic VIX calculation
/// </summary>
public record SyntheticVixWeights
{
    public decimal VxxCoefficient { get; init; }
    public decimal UvxyCoefficient { get; init; }
    public decimal SvxyCoefficient { get; init; }
    public decimal SpyCoefficient { get; init; }
    public decimal Intercept { get; init; }
    public DateTime TrainedAt { get; init; }
    public decimal RSquared { get; init; }
    public int SampleSize { get; init; }
}

/// <summary>
/// Snapshot of ETF prices used for synthetic VIX calculation
/// </summary>
public record EtfPriceSnapshot
{
    public decimal VxxPrice { get; init; }
    public decimal UvxyPrice { get; init; }
    public decimal SvxyPrice { get; init; }
    public decimal SpyPrice { get; init; }
    public DateTime Timestamp { get; init; }
    public TimeSpan DataAge { get; init; }
}
