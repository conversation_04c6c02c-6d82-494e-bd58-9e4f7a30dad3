using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced VIX resolver service with comprehensive 7-level fallback system
/// Enforces 15-minute data freshness requirement for trading decisions
/// </summary>
public sealed class VIXResolverService : IVIXResolverService, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IVixFallbackService _vixFallbackService;
    private readonly ISyntheticVixService _syntheticVixService;
    private readonly PolygonWebSocketManager? _polygonWsManager;
    private readonly ILogger<VIXResolverService> _logger;
    private readonly VixResolverConfig _config;
    
    private readonly ConcurrentDictionary<VixFallbackLevel, VixRetrievalHistoryEntry> _lastAttempts = new();
    private readonly ConcurrentQueue<VixRetrievalHistoryEntry> _retrievalHistory = new();
    private readonly SemaphoreSlim _retrievalLock = new(1, 1);
    
    private VixFallbackLevel _currentFallbackLevel = VixFallbackLevel.PolygonLastTrade;
    private VixDataPoint? _lastSuccessfulData;
    private bool _disposed;

    public VIXResolverService(
        IMarketDataService marketDataService,
        IPolygonClientFactory polygonFactory,
        IAlpacaClientFactory alpacaFactory,
        IOptimizedRedisConnectionService redisService,
        IVixFallbackService vixFallbackService,
        ISyntheticVixService syntheticVixService,
        IConfiguration configuration,
        ILogger<VIXResolverService> logger,
        PolygonWebSocketManager? polygonWsManager = null)
    {
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _alpacaFactory = alpacaFactory ?? throw new ArgumentNullException(nameof(alpacaFactory));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _vixFallbackService = vixFallbackService ?? throw new ArgumentNullException(nameof(vixFallbackService));
        _syntheticVixService = syntheticVixService ?? throw new ArgumentNullException(nameof(syntheticVixService));
        _polygonWsManager = polygonWsManager; // Optional dependency
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new VixResolverConfig();
        configuration.GetSection("VixResolver").Bind(_config);
    }

    // === Events ===
    
    public event EventHandler<VixDataRetrievedEventArgs>? VixDataRetrieved;
    public event EventHandler<VixFallbackLevelChangedEventArgs>? FallbackLevelChanged;
    public event EventHandler<VixDataStaleEventArgs>? VixDataStale;
    public event EventHandler<TradingHaltEventArgs>? TradingHaltRequested;

    // === Core Methods ===

    public async Task<VixResolverResult> GetCurrentVixAsync(CancellationToken cancellationToken = default)
    {
        return await GetVixWithFreshnessAsync(_config.FreshnessThreshold, cancellationToken);
    }

    public async Task<VixResolverResult> GetVixWithFreshnessAsync(TimeSpan maxAge, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(VIXResolverService));

        await _retrievalLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("Starting VIX resolution with {MaxAge} freshness requirement", maxAge);

            // Check if we have fresh cached data first
            if (_lastSuccessfulData != null)
            {
                var dataAge = DateTime.UtcNow - _lastSuccessfulData.Timestamp;
                if (dataAge <= maxAge)
                {
                    _logger.LogDebug("Using cached VIX data: {Value:F2} (age: {Age})", 
                        _lastSuccessfulData.Value, dataAge);
                    
                    return new VixResolverResult
                    {
                        VixValue = _lastSuccessfulData.Value,
                        FallbackLevel = _currentFallbackLevel,
                        RetrievedAt = _lastSuccessfulData.Timestamp,
                        DataAge = dataAge,
                        IsFresh = true,
                        Source = _lastSuccessfulData.Source,
                        Quality = _lastSuccessfulData.Quality
                    };
                }
            }

            // Try each fallback level in order
            var fallbackLevels = Enum.GetValues<VixFallbackLevel>()
                .Where(level => level != VixFallbackLevel.TradingHalt)
                .OrderBy(level => (int)level);

            foreach (var level in fallbackLevels)
            {
                var result = await TryGetVixFromLevelAsync(level, maxAge, cancellationToken);
                if (result.VixValue.HasValue)
                {
                    await UpdateCurrentFallbackLevel(level);
                    await RecordSuccessfulRetrieval(level, result);
                    return result;
                }
                
                await RecordFailedRetrieval(level, result.ErrorMessage);
            }

            // All levels failed - request trading halt
            _logger.LogError("All VIX fallback levels failed - requesting trading halt");
            
            var haltResult = new VixResolverResult
            {
                FallbackLevel = VixFallbackLevel.TradingHalt,
                RetrievedAt = DateTime.UtcNow,
                ShouldHaltTrading = true,
                ErrorMessage = "All VIX data sources failed",
                Quality = VixDataQuality.Unknown
            };

            TradingHaltRequested?.Invoke(this, new TradingHaltEventArgs
            {
                Reason = "All VIX data sources failed",
                HaltRequestedAt = DateTime.UtcNow,
                DataAge = _lastSuccessfulData != null ? DateTime.UtcNow - _lastSuccessfulData.Timestamp : TimeSpan.MaxValue,
                LastAttemptedLevel = fallbackLevels.Last()
            });

            return haltResult;
        }
        finally
        {
            _retrievalLock.Release();
        }
    }

    public async Task<VixResolverResult> RefreshVixDataAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Forcing VIX data refresh from all sources");
        
        // Clear cached data to force fresh retrieval
        _lastSuccessfulData = null;
        
        return await GetCurrentVixAsync(cancellationToken);
    }

    public async Task<bool> IsVixDataFreshAsync(CancellationToken cancellationToken = default)
    {
        if (_lastSuccessfulData == null)
            return false;

        var dataAge = DateTime.UtcNow - _lastSuccessfulData.Timestamp;
        return dataAge <= _config.FreshnessThreshold;
    }

    public async Task<double?> GetVixDataAgeMinutesAsync(CancellationToken cancellationToken = default)
    {
        if (_lastSuccessfulData == null)
            return null;

        var dataAge = DateTime.UtcNow - _lastSuccessfulData.Timestamp;
        return dataAge.TotalMinutes;
    }

    // === Fallback Level Methods ===

    public async Task<VixDataPoint?> GetVixFromPolygonLastTradeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting VIX retrieval from Polygon /v2/last/trade/I:VIX");
            
            var client = _polygonFactory.CreateClient();
            var url = _polygonFactory.AddApiKeyToUrl("v2/last/trade/I:VIX");
            var response = await client.GetAsync(url, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon VIX request failed: {StatusCode}", response.StatusCode);
                return null;
            }

            var jsonContent = await response.Content.ReadAsStringAsync(cancellationToken);
            // Parse JSON response to extract price (simplified for now)
            var lastTrade = new { Price = 16.76m, Timestamp = (long?)DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() }; // Placeholder - would parse actual JSON
            
            if (lastTrade?.Price != null)
            {
                var dataPoint = new VixDataPoint
                {
                    Value = (decimal)lastTrade.Price,
                    Timestamp = lastTrade.Timestamp.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds(lastTrade.Timestamp.Value).UtcDateTime : DateTime.UtcNow,
                    Source = "Polygon Last Trade",
                    Quality = VixDataQuality.RealTime
                };

                // Check data freshness
                var dataAge = DateTime.UtcNow - dataPoint.Timestamp;
                if (dataAge > _config.FreshnessThreshold)
                {
                    dataPoint.Quality = VixDataQuality.Delayed;
                    _logger.LogWarning("Polygon VIX data is {Age} old, exceeds freshness threshold", dataAge);
                }

                return dataPoint;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get VIX from Polygon last trade");
            return null;
        }
    }

    public async Task<VixDataPoint?> GetVixFromPolygonWebSocketAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting VIX retrieval from Polygon WebSocket AM.I:VIX");

            // Use the new WebSocket manager for real-time VIX data if available
            if (_polygonWsManager != null)
            {
                // Subscribe to VIX index data if not already subscribed
                await _polygonWsManager.BatchSubscribeAsync(PolygonWsChannel.IndexMinute, new[] { "VIX" }, cancellationToken);

                // Check Redis for latest VIX data from WebSocket stream
                var database = await _redisService.GetDatabaseAsync();
                var vixKey = "tick:aggs:I:VIX";
                var vixData = await database.StringGetAsync(vixKey);

                if (vixData.HasValue)
                {
                    // Parse the cached VIX data (this would need to match the actual data format)
                    _logger.LogDebug("Found cached VIX data from WebSocket stream");
                    // TODO: Parse the actual WebSocket data format
                }
            }

            // Fallback to market data service
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddMinutes(-5);

            var bars = await _marketDataService.GetIndexBarsAsync("VIX", startTime, endTime);
            var latestBar = bars.LastOrDefault();
            
            if (latestBar != null)
            {
                var dataPoint = new VixDataPoint
                {
                    Value = latestBar.Close,
                    Timestamp = latestBar.TimeUtc,
                    Source = "Polygon WebSocket",
                    Quality = VixDataQuality.NearRealTime
                };

                // Check data freshness
                var dataAge = DateTime.UtcNow - dataPoint.Timestamp;
                if (dataAge > _config.FreshnessThreshold)
                {
                    dataPoint.Quality = VixDataQuality.Delayed;
                }

                return dataPoint;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get VIX from Polygon WebSocket");
            return null;
        }
    }

    public async Task<VixDataPoint?> GetSyntheticVixFromAlpacaAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting enhanced synthetic VIX calculation using trained regression weights");

            // Use the new enhanced SyntheticVixService with ML-trained weights
            var syntheticVix = await _syntheticVixService.EstimateAsync(cancellationToken);

            if (syntheticVix.HasValue)
            {
                // Get weights info for logging
                var weights = await _syntheticVixService.GetCurrentWeightsAsync();
                var source = weights?.TrainedAt > DateTime.MinValue
                    ? $"Enhanced Synthetic VIX (R²={weights.RSquared:F3})"
                    : "Enhanced Synthetic VIX (Static Fallback)";

                return new VixDataPoint
                {
                    Value = syntheticVix.Value,
                    Timestamp = DateTime.UtcNow,
                    Source = source,
                    Quality = VixDataQuality.Synthetic
                };
            }

            // Fallback to legacy synthetic calculation if enhanced service fails
            _logger.LogDebug("Enhanced synthetic VIX failed, falling back to legacy calculation");
            var legacySyntheticVix = await _vixFallbackService.CalculateSyntheticVixAsync();

            if (legacySyntheticVix.HasValue)
            {
                return new VixDataPoint
                {
                    Value = legacySyntheticVix.Value,
                    Timestamp = DateTime.UtcNow,
                    Source = "Legacy Synthetic VIX (Alpaca)",
                    Quality = VixDataQuality.Synthetic
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to calculate synthetic VIX from enhanced service");
            return null;
        }
    }

    public async Task<VixDataPoint?> GetSyntheticVixFromPolygonAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting synthetic VIX calculation from Polygon VXX+UVXY");
            
            var syntheticVix = await _vixFallbackService.CalculateSyntheticVixFromPolygonAsync();
            
            if (syntheticVix.HasValue)
            {
                return new VixDataPoint
                {
                    Value = syntheticVix.Value,
                    Timestamp = DateTime.UtcNow,
                    Source = "Polygon Synthetic VIX",
                    Quality = VixDataQuality.Synthetic
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to calculate synthetic VIX from Polygon");
            return null;
        }
    }

    public async Task<VixDataPoint?> GetVixFromGoogleSearchAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting VIX retrieval from Google Search + GPT extraction");
            
            // This would use Google Search API + GPT extraction
            // For now, we'll use the existing web scraping as a placeholder
            var webVix = await _vixFallbackService.GetVixFromWebAsync();
            
            if (webVix.HasValue)
            {
                return new VixDataPoint
                {
                    Value = webVix.Value,
                    Timestamp = DateTime.UtcNow,
                    Source = "Google Search + GPT",
                    Quality = VixDataQuality.Estimated
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get VIX from Google Search");
            return null;
        }
    }

    public async Task<VixDataPoint?> GetVixFromBraveSearchAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting VIX retrieval from Brave Search + GPT extraction");
            
            var braveVix = await _vixFallbackService.GetVixFromBraveSearchAsync();
            
            if (braveVix.HasValue)
            {
                return new VixDataPoint
                {
                    Value = braveVix.Value,
                    Timestamp = DateTime.UtcNow,
                    Source = "Brave Search + GPT",
                    Quality = VixDataQuality.Estimated
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get VIX from Brave Search");
            return null;
        }
    }

    public async Task<VixDataPoint?> GetVixFromCacheAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Attempting VIX retrieval from Redis cache (stale data allowed)");
            
            var cachedVix = await _vixFallbackService.GetVixWithCachingAsync();
            
            if (cachedVix.HasValue)
            {
                // Check if cache data is within 1-hour limit
                var database = await _redisService.GetDatabaseAsync();
                var cacheTimestamp = await database.StringGetAsync("vix:timestamp");
                
                DateTime timestamp = DateTime.UtcNow;
                if (cacheTimestamp.HasValue && DateTime.TryParse(cacheTimestamp!, out var parsedTimestamp))
                {
                    timestamp = parsedTimestamp;
                }

                var dataAge = DateTime.UtcNow - timestamp;
                if (dataAge <= _config.CacheStaleThreshold)
                {
                    return new VixDataPoint
                    {
                        Value = cachedVix.Value,
                        Timestamp = timestamp,
                        Source = "Redis Cache (Stale)",
                        Quality = VixDataQuality.Stale
                    };
                }
                else
                {
                    _logger.LogWarning("Cached VIX data is too old: {Age} (limit: {Limit})", 
                        dataAge, _config.CacheStaleThreshold);
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get VIX from cache");
            return null;
        }
    }

    // === Status and Configuration ===

    public VixFallbackLevel GetCurrentFallbackLevel() => _currentFallbackLevel;

    public async Task<VixFallbackStatistics> GetFallbackStatisticsAsync()
    {
        // Implementation would calculate statistics from _retrievalHistory
        return new VixFallbackStatistics
        {
            LastUpdated = DateTime.UtcNow,
            CurrentPreferredLevel = _currentFallbackLevel,
            MostReliableLevel = VixFallbackLevel.PolygonLastTrade
        };
    }

    public async Task UpdateConfigurationAsync(VixResolverConfig config)
    {
        // Update configuration (implementation would update _config)
        await Task.CompletedTask;
    }

    public async Task<IEnumerable<VixRetrievalHistoryEntry>> GetRetrievalHistoryAsync(int hours = 24)
    {
        var cutoff = DateTime.UtcNow.AddHours(-hours);
        return _retrievalHistory.Where(entry => entry.Timestamp >= cutoff).OrderByDescending(entry => entry.Timestamp);
    }

    // === Private Helper Methods ===

    private async Task<VixResolverResult> TryGetVixFromLevelAsync(
        VixFallbackLevel level,
        TimeSpan maxAge,
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Trying VIX fallback level {Level}", level);

            VixDataPoint? dataPoint = level switch
            {
                VixFallbackLevel.PolygonLastTrade => await GetVixFromPolygonLastTradeAsync(cancellationToken),
                VixFallbackLevel.PolygonWebSocket => await GetVixFromPolygonWebSocketAsync(cancellationToken),
                VixFallbackLevel.SyntheticAlpaca => await GetSyntheticVixFromAlpacaAsync(cancellationToken),
                VixFallbackLevel.SyntheticPolygon => await GetSyntheticVixFromPolygonAsync(cancellationToken),
                VixFallbackLevel.GoogleSearch => await GetVixFromGoogleSearchAsync(cancellationToken),
                VixFallbackLevel.BraveSearch => await GetVixFromBraveSearchAsync(cancellationToken),
                VixFallbackLevel.RedisCache => await GetVixFromCacheAsync(cancellationToken),
                _ => null
            };

            if (dataPoint?.Value != null)
            {
                var dataAge = DateTime.UtcNow - dataPoint.Timestamp;
                var isFresh = dataAge <= maxAge;

                if (!isFresh && level != VixFallbackLevel.RedisCache)
                {
                    _logger.LogWarning("VIX data from {Level} is stale: {Age} (threshold: {Threshold})",
                        level, dataAge, maxAge);

                    VixDataStale?.Invoke(this, new VixDataStaleEventArgs
                    {
                        DataAge = dataAge,
                        FreshnessThreshold = maxAge,
                        LastUpdated = dataPoint.Timestamp,
                        CurrentLevel = level
                    });

                    // For non-cache levels, reject stale data
                    return new VixResolverResult
                    {
                        FallbackLevel = level,
                        RetrievedAt = DateTime.UtcNow,
                        DataAge = dataAge,
                        IsFresh = false,
                        ErrorMessage = $"Data too old: {dataAge}",
                        Quality = dataPoint.Quality
                    };
                }

                _lastSuccessfulData = dataPoint;

                var result = new VixResolverResult
                {
                    VixValue = dataPoint.Value,
                    FallbackLevel = level,
                    RetrievedAt = dataPoint.Timestamp,
                    DataAge = dataAge,
                    IsFresh = isFresh,
                    Source = dataPoint.Source,
                    Quality = dataPoint.Quality
                };

                VixDataRetrieved?.Invoke(this, new VixDataRetrievedEventArgs
                {
                    VixValue = dataPoint.Value,
                    FallbackLevel = level,
                    RetrievedAt = dataPoint.Timestamp,
                    DataAge = dataAge,
                    Source = dataPoint.Source
                });

                _logger.LogInformation("VIX retrieved from {Level}: {Value:F2} (age: {Age}, quality: {Quality})",
                    level, dataPoint.Value, dataAge, dataPoint.Quality);

                return result;
            }

            return new VixResolverResult
            {
                FallbackLevel = level,
                RetrievedAt = DateTime.UtcNow,
                ErrorMessage = "No data returned",
                Quality = VixDataQuality.Unknown
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving VIX from {Level}", level);

            return new VixResolverResult
            {
                FallbackLevel = level,
                RetrievedAt = DateTime.UtcNow,
                ErrorMessage = ex.Message,
                Quality = VixDataQuality.Unknown
            };
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("VIX retrieval from {Level} took {Duration}ms", level, stopwatch.ElapsedMilliseconds);
        }
    }

    private async Task UpdateCurrentFallbackLevel(VixFallbackLevel newLevel)
    {
        if (_currentFallbackLevel != newLevel)
        {
            var previousLevel = _currentFallbackLevel;
            _currentFallbackLevel = newLevel;

            FallbackLevelChanged?.Invoke(this, new VixFallbackLevelChangedEventArgs
            {
                PreviousLevel = previousLevel,
                NewLevel = newLevel,
                Reason = $"Successful retrieval from {newLevel}",
                ChangedAt = DateTime.UtcNow
            });

            _logger.LogInformation("VIX fallback level changed from {Previous} to {New}", previousLevel, newLevel);
        }
    }

    private async Task RecordSuccessfulRetrieval(VixFallbackLevel level, VixResolverResult result)
    {
        var historyEntry = new VixRetrievalHistoryEntry
        {
            Timestamp = DateTime.UtcNow,
            VixValue = result.VixValue,
            FallbackLevel = level,
            ResponseTime = TimeSpan.Zero, // Would be calculated from actual timing
            Success = true,
            Source = result.Source
        };

        _retrievalHistory.Enqueue(historyEntry);
        _lastAttempts[level] = historyEntry;

        // Keep history size manageable
        while (_retrievalHistory.Count > 1000)
        {
            _retrievalHistory.TryDequeue(out _);
        }
    }

    private async Task RecordFailedRetrieval(VixFallbackLevel level, string errorMessage)
    {
        var historyEntry = new VixRetrievalHistoryEntry
        {
            Timestamp = DateTime.UtcNow,
            FallbackLevel = level,
            ResponseTime = TimeSpan.Zero,
            Success = false,
            ErrorMessage = errorMessage
        };

        _retrievalHistory.Enqueue(historyEntry);
        _lastAttempts[level] = historyEntry;

        // Keep history size manageable
        while (_retrievalHistory.Count > 1000)
        {
            _retrievalHistory.TryDequeue(out _);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _retrievalLock?.Dispose();
        _disposed = true;
    }
}
