using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using StackExchange.Redis;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class DynamicUniverseProviderTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<ILogger<DynamicUniverseProvider>> _mockLogger;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly IConfiguration _configuration;
    private readonly TestableDynamicUniverseProvider _service;

    public DynamicUniverseProviderTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockLogger = new Mock<ILogger<DynamicUniverseProvider>>();
        _mockRedis = new Mock<IDatabase>();

        // Setup configuration
        var configData = new Dictionary<string, string>
        {
            {"REDIS_URL", "localhost:6379"},
            {"REDIS_DATABASE", "0"}
        };
        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        _service = new TestableDynamicUniverseProvider(_mockMarketDataService.Object, _configuration, _mockLogger.Object, _mockRedis.Object);
    }

    [Fact]
    public async Task BuildUniverseAsync_WithQualifyingSymbols_ShouldReturnFilteredList()
    {
        // Arrange
        var candidateSymbols = new[] { "AAPL", "MSFT", "LOWPRICE", "LOWVOL", "LOWVOLATILITY" };
        
        SetupMarketDataForSymbol("AAPL", price: 150m, volume: 2_000_000, volatility: 5.0m);
        SetupMarketDataForSymbol("MSFT", price: 300m, volume: 1_500_000, volatility: 4.0m);
        SetupMarketDataForSymbol("LOWPRICE", price: 5m, volume: 2_000_000, volatility: 5.0m); // Fails price filter
        SetupMarketDataForSymbol("LOWVOL", price: 100m, volume: 500_000, volatility: 5.0m); // Fails volume filter
        SetupMarketDataForSymbol("LOWVOLATILITY", price: 100m, volume: 2_000_000, volatility: 0.5m); // Fails volatility filter

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(2);
        resultList.Should().Contain("AAPL");
        resultList.Should().Contain("MSFT");
        resultList.Should().NotContain("LOWPRICE");
        resultList.Should().NotContain("LOWVOL");
        resultList.Should().NotContain("LOWVOLATILITY");
    }

    [Fact]
    public async Task BuildUniverseAsync_WithNoQualifyingSymbols_ShouldReturnEmptyList()
    {
        // Arrange
        var candidateSymbols = new[] { "LOWPRICE", "LOWVOL" };
        
        SetupMarketDataForSymbol("LOWPRICE", price: 5m, volume: 2_000_000, volatility: 3.0m);
        SetupMarketDataForSymbol("LOWVOL", price: 100m, volume: 500_000, volatility: 3.0m);

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task BuildUniverseAsync_WithApiErrors_ShouldSkipErroredSymbols()
    {
        // Arrange
        var candidateSymbols = new[] { "AAPL", "ERROR_SYMBOL", "MSFT" };
        
        SetupMarketDataForSymbol("AAPL", price: 150m, volume: 2_000_000, volatility: 5.0m);
        SetupMarketDataForSymbol("MSFT", price: 300m, volume: 1_500_000, volatility: 4.0m);
        
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync($1, $2, $3), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(2);
        resultList.Should().Contain("AAPL");
        resultList.Should().Contain("MSFT");
        resultList.Should().NotContain("ERROR_SYMBOL");
    }

    [Fact]
    public async Task GetCachedUniverseAsync_WithValidCache_ShouldReturnCachedSymbols()
    {
        // Arrange
        var universeData = new RedisUniverse
        {
            Symbols = new List<string> { "AAPL", "MSFT", "GOOGL" },
            GeneratedAt = DateTime.UtcNow.AddHours(-1),
            CandidateCount = 10,
            QualifiedCount = 3
        };

        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(universeData.ToJson());

        // Act
        var result = await _service.GetCachedUniverseAsync();
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(3);
        resultList.Should().Contain("AAPL");
        resultList.Should().Contain("MSFT");
        resultList.Should().Contain("GOOGL");
    }

    [Fact]
    public async Task GetCachedUniverseAsync_WithNoCache_ShouldBuildNewUniverse()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(RedisValue.Null);

        // Setup default candidates to return some qualifying symbols
        var defaultCandidates = _service.GetDefaultCandidates().Take(2).ToArray();
        foreach (var symbol in defaultCandidates)
        {
            SetupMarketDataForSymbol(symbol, price: 150m, volume: 2_000_000, volatility: 5.0m);
        }

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.GetCachedUniverseAsync();

        // Assert
        result.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetUniverseDetailsAsync_WithValidCache_ShouldReturnDetails()
    {
        // Arrange
        var universeData = new RedisUniverse
        {
            Symbols = new List<string> { "AAPL", "MSFT" },
            GeneratedAt = DateTime.UtcNow.AddHours(-1),
            CandidateCount = 10,
            QualifiedCount = 2,
            FilterCriteria = new UniverseFilterCriteria { MinPrice = 10m },
            Metrics = new UniverseGenerationMetrics { GenerationTime = TimeSpan.FromSeconds(5) }
        };

        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(universeData.ToJson());

        // Act
        var result = await _service.GetUniverseDetailsAsync();

        // Assert
        result.Should().NotBeNull();
        result!.Symbols.Should().HaveCount(2);
        result.CandidateCount.Should().Be(10);
        result.QualifiedCount.Should().Be(2);
        result.FilterCriteria.MinPrice.Should().Be(10m);
        result.Metrics.GenerationTime.Should().Be(TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task IsCacheValidAsync_WithRecentCache_ShouldReturnTrue()
    {
        // Arrange
        var universeData = new RedisUniverse
        {
            Symbols = new List<string> { "AAPL" },
            GeneratedAt = DateTime.UtcNow.AddHours(-1) // 1 hour ago
        };

        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(universeData.ToJson());

        // Act
        var result = await _service.IsCacheValidAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsCacheValidAsync_WithExpiredCache_ShouldReturnFalse()
    {
        // Arrange
        var universeData = new RedisUniverse
        {
            Symbols = new List<string> { "AAPL" },
            GeneratedAt = DateTime.UtcNow.AddHours(-25) // 25 hours ago (expired)
        };

        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(universeData.ToJson());

        // Act
        var result = await _service.IsCacheValidAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsCacheValidAsync_WithNoCache_ShouldReturnFalse()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _service.IsCacheValidAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetDefaultCandidates_ShouldReturnNonEmptyList()
    {
        // Act
        var result = _service.GetDefaultCandidates();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("AAPL");
        result.Should().Contain("MSFT");
        result.Should().Contain("SPY");
    }

    [Fact]
    public async Task RefreshUniverseAsync_ShouldForceRebuild()
    {
        // Arrange
        var candidateSymbols = new[] { "AAPL", "MSFT" };

        SetupMarketDataForSymbol("AAPL", price: 150m, volume: 2_000_000, volatility: 5.0m);
        SetupMarketDataForSymbol("MSFT", price: 300m, volume: 1_500_000, volatility: 4.0m);

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.RefreshUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(2);
        resultList.Should().Contain("AAPL");
        resultList.Should().Contain("MSFT");
    }

    [Fact]
    public async Task BuildUniverseAsync_WithEmptyInput_ShouldUseDefaultCandidates()
    {
        // Arrange
        var defaultCandidates = _service.GetDefaultCandidates().Take(3).ToArray();
        foreach (var symbol in defaultCandidates)
        {
            SetupMarketDataForSymbol(symbol, price: 150m, volume: 2_000_000, volatility: 5.0m);
        }

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(null);
        var resultList = result.ToList();

        // Assert
        resultList.Should().NotBeEmpty();
        resultList.Should().HaveCountGreaterOrEqualTo(3);
    }

    [Fact]
    public async Task BuildUniverseAsync_WithInsufficientData_ShouldSkipSymbol()
    {
        // Arrange
        var candidateSymbols = new[] { "AAPL", "INSUFFICIENT_DATA" };

        SetupMarketDataForSymbol("AAPL", price: 150m, volume: 2_000_000, volatility: 5.0m);

        // Setup insufficient data for second symbol
        var insufficientBars = CreateMockBars(100m, 1_000_000, 3.0m).Take(5).ToList(); // Only 5 bars
        var mockPage = CreateMockPage(insufficientBars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync($1, $2, $3), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(1);
        resultList.Should().Contain("AAPL");
        resultList.Should().NotContain("INSUFFICIENT_DATA");
    }

    [Fact]
    public async Task BuildUniverseAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var candidateSymbols = new[] { "AAPL", "MSFT" };

        SetupMarketDataForSymbol("AAPL", price: 150m, volume: 2_000_000, volatility: 5.0m);
        SetupMarketDataForSymbol("MSFT", price: 300m, volume: 1_500_000, volatility: 4.0m);

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act - Simulate concurrent calls
        var tasks = new List<Task<IEnumerable<string>>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.BuildUniverseAsync(candidateSymbols));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(5);
        foreach (var result in results)
        {
            var resultList = result.ToList();
            resultList.Should().HaveCount(2);
            resultList.Should().Contain("AAPL");
            resultList.Should().Contain("MSFT");
        }
    }

    [Fact]
    public async Task GetCachedUniverseAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var universeData = new RedisUniverse
        {
            Symbols = new List<string> { "AAPL", "MSFT" },
            GeneratedAt = DateTime.UtcNow.AddHours(-1)
        };

        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(universeData.ToJson());

        // Act - Simulate concurrent calls
        var tasks = new List<Task<IEnumerable<string>>>();
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(_service.GetCachedUniverseAsync());
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(10);
        foreach (var result in results)
        {
            var resultList = result.ToList();
            resultList.Should().HaveCount(2);
            resultList.Should().Contain("AAPL");
            resultList.Should().Contain("MSFT");
        }
    }

    [Fact]
    public async Task GetCachedUniverseAsync_WithCorruptedCache_ShouldRebuildUniverse()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync("invalid_json_data");

        // Setup default candidates for rebuild
        var defaultCandidates = _service.GetDefaultCandidates().Take(2).ToArray();
        foreach (var symbol in defaultCandidates)
        {
            SetupMarketDataForSymbol(symbol, price: 150m, volume: 2_000_000, volatility: 5.0m);
        }

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.GetCachedUniverseAsync();

        // Assert
        result.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetCachedUniverseAsync_WithRedisException_ShouldRebuildUniverse()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ThrowsAsync(new Exception("Redis connection failed"));

        // Setup default candidates for rebuild
        var defaultCandidates = _service.GetDefaultCandidates().Take(2).ToArray();
        foreach (var symbol in defaultCandidates)
        {
            SetupMarketDataForSymbol(symbol, price: 150m, volume: 2_000_000, volatility: 5.0m);
        }

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.GetCachedUniverseAsync();

        // Assert
        result.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetUniverseDetailsAsync_WithNoCache_ShouldReturnNull()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _service.GetUniverseDetailsAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetUniverseDetailsAsync_WithException_ShouldReturnNull()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ThrowsAsync(new Exception("Redis error"));

        // Act
        var result = await _service.GetUniverseDetailsAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task IsCacheValidAsync_WithException_ShouldReturnFalse()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ThrowsAsync(new Exception("Redis error"));

        // Act
        var result = await _service.IsCacheValidAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsCacheValidAsync_WithCorruptedData_ShouldReturnFalse()
    {
        // Arrange
        _mockRedis.Setup(x => x.StringGetAsync(RedisUniverse.GetRedisKey(), default(CommandFlags)))
            .ReturnsAsync("invalid_json");

        // Act
        var result = await _service.IsCacheValidAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData(5.0, 2_000_000, 5.0, false)] // Fails price filter
    [InlineData(50.0, 500_000, 5.0, false)] // Fails volume filter
    [InlineData(50.0, 2_000_000, 0.5, false)] // Fails volatility filter
    [InlineData(50.0, 2_000_000, 5.0, true)] // Passes all filters
    public async Task BuildUniverseAsync_WithVariousFilterCriteria_ShouldFilterCorrectly(
        decimal price, long volume, decimal volatility, bool shouldPass)
    {
        // Arrange
        var candidateSymbols = new[] { "TEST_SYMBOL" };
        SetupMarketDataForSymbol("TEST_SYMBOL", price, volume, volatility);

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        if (shouldPass)
        {
            resultList.Should().Contain("TEST_SYMBOL");
        }
        else
        {
            resultList.Should().NotContain("TEST_SYMBOL");
        }
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Database)]
    public async Task BuildUniverseAsync_WithLargeSymbolSet_ShouldHandleEfficiently()
    {
        // Arrange - Reduced from 100 to 20 symbols for faster test execution
        var candidateSymbols = Enumerable.Range(1, 20).Select(i => $"SYM{i:D2}").ToArray();

        foreach (var symbol in candidateSymbols.Take(10)) // Only half qualify
        {
            SetupMarketDataForSymbol(symbol, price: 150m, volume: 2_000_000, volatility: 5.0m);
        }

        foreach (var symbol in candidateSymbols.Skip(10)) // Half don't qualify
        {
            SetupMarketDataForSymbol(symbol, price: 5m, volume: 2_000_000, volatility: 5.0m); // Low price
        }

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(10);
        resultList.Should().OnlyContain(s => s.StartsWith("SYM") && int.Parse(s.Substring(3)) <= 10);
    }

    [Fact]
    public async Task BuildUniverseAsync_WithMixedApiResponses_ShouldHandlePartialFailures()
    {
        // Arrange
        var candidateSymbols = new[] { "GOOD1", "ERROR1", "GOOD2", "ERROR2", "GOOD3" };

        SetupMarketDataForSymbol("GOOD1", price: 150m, volume: 2_000_000, volatility: 5.0m);
        SetupMarketDataForSymbol("GOOD2", price: 200m, volume: 1_500_000, volatility: 4.0m);
        SetupMarketDataForSymbol("GOOD3", price: 100m, volume: 2_500_000, volatility: 5.0m);

        _mockMarketDataService.Setup(x => x.GetStockBarsAsync($1, $2, $3), default(DateTime)))
            .ThrowsAsync(new Exception("API Error 2"));

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(3);
        resultList.Should().Contain("GOOD1");
        resultList.Should().Contain("GOOD2");
        resultList.Should().Contain("GOOD3");
        resultList.Should().NotContain("ERROR1");
        resultList.Should().NotContain("ERROR2");
    }

    [Fact]
    public async Task BuildUniverseAsync_WithRedisFailure_ShouldStillReturnResults()
    {
        // Arrange
        var candidateSymbols = new[] { "AAPL", "MSFT" };

        SetupMarketDataForSymbol("AAPL", price: 150m, volume: 2_000_000, volatility: 5.0m);
        SetupMarketDataForSymbol("MSFT", price: 300m, volume: 1_500_000, volatility: 4.0m);

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ThrowsAsync(new Exception("Redis write failed"));

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().HaveCount(2);
        resultList.Should().Contain("AAPL");
        resultList.Should().Contain("MSFT");
    }

    [Fact]
    public async Task RefreshUniverseAsync_WithNullInput_ShouldUseDefaultCandidates()
    {
        // Arrange
        var defaultCandidates = _service.GetDefaultCandidates().Take(3).ToArray();
        foreach (var symbol in defaultCandidates)
        {
            SetupMarketDataForSymbol(symbol, price: 150m, volume: 2_000_000, volatility: 5.0m);
        }

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.RefreshUniverseAsync(null);
        var resultList = result.ToList();

        // Assert
        resultList.Should().NotBeEmpty();
        resultList.Should().HaveCountGreaterOrEqualTo(3);
    }

    [Fact]
    public async Task BuildUniverseAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.BuildUniverseAsync(new[] { "AAPL" }, cts.Token));
    }

    [Fact]
    public async Task GetCachedUniverseAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.GetCachedUniverseAsync(cts.Token));
    }

    [Fact]
    public async Task GetUniverseDetailsAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.GetUniverseDetailsAsync(cts.Token));
    }

    [Fact]
    public async Task IsCacheValidAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.IsCacheValidAsync(cts.Token));
    }

    [Fact]
    public async Task RefreshUniverseAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _service.RefreshUniverseAsync(new[] { "AAPL" }, cts.Token));
    }

    [Fact]
    public void GetDefaultCandidates_ShouldReturnConsistentResults()
    {
        // Act
        var result1 = _service.GetDefaultCandidates();
        var result2 = _service.GetDefaultCandidates();

        // Assert
        result1.Should().BeEquivalentTo(result2);
        result1.Should().NotBeEmpty();
        result1.Should().HaveCountGreaterOrEqualTo(10);
    }

    [Fact]
    public async Task BuildUniverseAsync_WithZeroVolatilityData_ShouldHandleGracefully()
    {
        // Arrange
        var candidateSymbols = new[] { "ZERO_VOL" };

        // Create bars with identical prices (zero volatility) - reduced from 20 to 10 for speed
        var bars = new List<IBar>();
        for (int i = 0; i < 10; i++)
        {
            var mockBar = new Mock<IBar>();
            mockBar.Setup(x => x.Close).Returns(100m); // Same price every day
            mockBar.Setup(x => x.High).Returns(100m);
            mockBar.Setup(x => x.Low).Returns(100m);
            mockBar.Setup(x => x.Open).Returns(100m);
            mockBar.Setup(x => x.Volume).Returns(2_000_000);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-10 + i));
            bars.Add(mockBar.Object);
        }

        var mockPage = CreateMockPage(bars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync($1, $2, $3), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().NotContain("ZERO_VOL"); // Should be filtered out due to zero volatility
    }

    [Fact]
    public async Task BuildUniverseAsync_WithExtremeVolatilityData_ShouldIncludeSymbol()
    {
        // Arrange
        var candidateSymbols = new[] { "HIGH_VOL" };
        SetupMarketDataForSymbol("HIGH_VOL", price: 100m, volume: 2_000_000, volatility: 20.0m); // Very high volatility

        _mockRedis.Setup(x => x.StringSetAsync(default(RedisKey), default(RedisValue), default(TimeSpan?), default(When), default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        var result = await _service.BuildUniverseAsync(candidateSymbols);
        var resultList = result.ToList();

        // Assert
        resultList.Should().Contain("HIGH_VOL"); // Should pass volatility filter
    }

    private void SetupMarketDataForSymbol(string symbol, decimal price, long volume, decimal volatility)
    {
        var bars = CreateMockBars(price, volume, volatility);
        var mockPage = CreateMockPage(bars);
        
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync($1, $2, $3)
    {
        var bars = new List<IBar>();
        var random = new Random(42);

        // Reduced from 20 to 10 days for faster test execution
        for (int i = 0; i < 10; i++)
        {
            // Create volatility that will meet the 2% threshold
            var dailyReturn = (decimal)(random.NextDouble() - 0.5) * (volatility / 100m) * 2; // Amplify volatility
            var dayPrice = Math.Max(1m, price * (1 + dailyReturn)); // Ensure positive price

            var mockBar = new Mock<IBar>();
            mockBar.Setup(x => x.Close).Returns(dayPrice);
            mockBar.Setup(x => x.High).Returns(dayPrice * 1.02m);
            mockBar.Setup(x => x.Low).Returns(dayPrice * 0.98m);
            mockBar.Setup(x => x.Open).Returns(dayPrice);
            mockBar.Setup(x => x.Volume).Returns(volume);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-10 + i));
            mockBar.Setup(x => x.Vwap).Returns(0);
            mockBar.Setup(x => x.TradeCount).Returns(0);

            bars.Add(mockBar.Object);
        }

        return bars;
    }

    private IPage<IBar> CreateMockPage(List<IBar> bars)
    {
        var mockPage = new Mock<IPage<IBar>>();
        mockPage.Setup(x => x.Items).Returns(bars);
        return mockPage.Object;
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}

/// <summary>
/// Testable version of DynamicUniverseProvider that allows injecting a mock Redis database
/// </summary>
public class TestableDynamicUniverseProvider : IDynamicUniverseProvider, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IDatabase _redis;
    private readonly ILogger<DynamicUniverseProvider> _logger;
    private readonly UniverseFilterCriteria _defaultCriteria;

    // Same default candidates as the main service
    private static readonly string[] DefaultCandidates = new[]
    {
        "SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA"
    };

    public TestableDynamicUniverseProvider(
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<DynamicUniverseProvider> logger,
        IDatabase redis)
    {
        _marketDataService = marketDataService;
        _redis = redis;
        _logger = logger;

        _defaultCriteria = new UniverseFilterCriteria
        {
            MinPrice = 10.0m,
            MinAverageVolume = 1_000_000,
            MinVolatilityPercent = 2.0m,
            AnalysisPeriodDays = 20,
            MaxSymbols = 200
        };
    }

    public async Task<IEnumerable<string>> BuildUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        var candidates = (candidateSymbols ?? DefaultCandidates).ToList();
        var qualified = new List<string>();

        foreach (var symbol in candidates)
        {
            try
            {
                var startDate = DateTime.UtcNow.AddDays(-_defaultCriteria.AnalysisPeriodDays);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync($1, $2, $3);
            }
        }

        // Cache the result
        var universeData = new RedisUniverse
        {
            Symbols = qualified,
            GeneratedAt = DateTime.UtcNow,
            CandidateCount = candidates.Count,
            QualifiedCount = qualified.Count,
            FilterCriteria = _defaultCriteria,
            Metrics = new UniverseGenerationMetrics(),
            Metadata = $"Test universe with {qualified.Count} symbols"
        };

        await _redis.StringSetAsync(RedisUniverse.GetRedisKey(), universeData.ToJson(), TimeSpan.FromHours(24));
        return qualified;
    }

    public async Task<IEnumerable<string>> GetCachedUniverseAsync(CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        try
        {
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return await BuildUniverseAsync(null, cancellationToken);
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                return await BuildUniverseAsync(null, cancellationToken);
            }

            return universeData.Symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }
    }

    public async Task<RedisUniverse?> GetUniverseDetailsAsync(CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        try
        {
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            return universeJson.HasValue ? RedisUniverse.FromJson(universeJson!) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving universe details");
            return null;
        }
    }

    public async Task<IEnumerable<string>> RefreshUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        return await BuildUniverseAsync(candidateSymbols, cancellationToken);
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        try
        {
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue) return false;

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null) return false;

            var cacheAge = DateTime.UtcNow - universeData.GeneratedAt;
            return cacheAge < TimeSpan.FromHours(24);
        }
        catch
        {
            return false;
        }
    }

    public IEnumerable<string> GetDefaultCandidates()
    {
        return DefaultCandidates;
    }

    private bool PassesPriceFilter(decimal price) => price >= _defaultCriteria.MinPrice;

    private bool PassesVolumeFilter(List<long> volumes)
    {
        if (volumes.Count == 0) return false;
        return volumes.Average() >= _defaultCriteria.MinAverageVolume;
    }

    private bool PassesVolatilityFilter(List<decimal> closes)
    {
        if (closes.Count < 2) return false;

        var returns = new List<decimal>();
        for (int i = 1; i < closes.Count; i++)
        {
            var dailyReturn = (closes[i] - closes[i - 1]) / closes[i - 1];
            returns.Add(Math.Abs(dailyReturn));
        }

        if (returns.Count == 0) return false;

        var averageVolatility = returns.Average();
        var volatilityPercent = averageVolatility * 100;
        return volatilityPercent >= _defaultCriteria.MinVolatilityPercent;
    }

    public void Dispose()
    {
        // Nothing to dispose in test version
    }
}
