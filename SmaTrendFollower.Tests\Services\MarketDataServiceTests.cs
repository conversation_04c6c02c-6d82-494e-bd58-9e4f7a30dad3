using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using System.Text;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class MarketDataServiceTests
{
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<ILogger<MarketDataService>> _mockLogger;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly MarketDataService _marketDataService;

    public MarketDataServiceTests()
    {
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockLogger = new Mock<ILogger<MarketDataService>>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockHttpClient = new Mock<HttpClient>();

        // Set up rate limit helpers
        var mockAlpacaRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();
        var mockPolygonRateLimitHelper = new Mock<IPolygonRateLimitHelper>();

        // Set up rate limit helpers to execute actions directly (no rate limiting in tests)
        // Set up specific types that are used in the MarketDataService
        // Note: The actual implementation returns List<T> from .ToList(), not IReadOnlyList<T>
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IPage<IBar>>>>(), default(string)))
            .Returns<Func<Task<IPage<IBar>>>, string>((func, key) => func());
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IAccount>>>(), default(string)))
            .Returns<Func<Task<IAccount>>, string>((func, key) => func());
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<List<IPosition>>>>(), default(string)))
            .Returns<Func<Task<List<IPosition>>>, string>((func, key) => func());
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<List<IOrder>>>>(), default(string)))
            .Returns<Func<Task<List<IOrder>>>, string>((func, key) => func());

        // Set up generic ExecuteAsync method for Polygon rate limit helper
        mockPolygonRateLimitHelper.Setup(x => x.ExecuteAsync<IPage<IBar>>(It.IsAny<Func<Task<IPage<IBar>>>>(), default(string)))
            .Returns<Func<Task<IPage<IBar>>>, string>((func, key) => func());
        mockPolygonRateLimitHelper.Setup(x => x.ExecuteAsync<decimal?>(It.IsAny<Func<Task<decimal?>>>(), default(string)))
            .Returns<Func<Task<decimal?>>, string>((func, key) => func());

        _mockAlpacaFactory.Setup(x => x.CreateDataClient()).Returns(_mockDataClient.Object);
        _mockAlpacaFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockAlpacaFactory.Setup(x => x.GetRateLimitHelper()).Returns(mockAlpacaRateLimitHelper.Object);

        _mockPolygonFactory.Setup(x => x.CreateClient()).Returns(_mockHttpClient.Object);
        _mockPolygonFactory.Setup(x => x.GetRateLimitHelper()).Returns(mockPolygonRateLimitHelper.Object);

        var mockIndexCacheService = new Mock<IIndexCacheService>();
        var mockStockBarCacheService = new Mock<IStockBarCacheService>();
        var mockVixFallbackService = new Mock<IVixFallbackService>();
        _marketDataService = new MarketDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            mockIndexCacheService.Object,
            mockStockBarCacheService.Object,
            _mockLogger.Object,
            mockVixFallbackService.Object);
    }

    [TestTimeout(TestTimeouts.Network)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetStockBarsAsync_WithValidSymbol_ReturnsAlpacaBars()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;
        
        var mockBars = CreateMockBars(10);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(mockBars);
        
        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(default(HistoricalBarsRequest), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _marketDataService.GetStockBarsAsync($1, $2, $3), Times.Once);
    }

    [TestTimeout(TestTimeouts.Network)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetStockBarsAsync_WithMultipleSymbols_ReturnsAllValidBars()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;

        var mockBars = CreateMockBars(5);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(mockBars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(default(HistoricalBarsRequest), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _marketDataService.GetStockBarsAsync($1, $2, $3)]
    [Fact]
    public async Task GetStockMinuteBarsAsync_WithValidSymbol_ReturnsMinuteBars()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddHours(-6);
        var endDate = DateTime.UtcNow;

        // Use smaller count for faster test execution - test the functionality, not the exact count
        var expectedCount = 60; // 1 hour of minute bars for faster testing
        var mockBars = CreateMockBars(expectedCount);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(mockBars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(
            It.Is<HistoricalBarsRequest>(r => r.TimeFrame == BarTimeFrame.Minute), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(expectedCount);
        result.Items.Should().AllSatisfy(bar => bar.Symbol.Should().Be("TEST"));
    }

    [TestTimeout(TestTimeouts.Network)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetAccountAsync_ReturnsAccountInformation()
    {
        // Arrange
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(100000m);
        mockAccount.Setup(x => x.BuyingPower).Returns(200000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var result = await _marketDataService.GetAccountAsync();

        // Assert
        result.Should().NotBeNull();
        result.Equity.Should().Be(100000m);
        result.BuyingPower.Should().Be(200000m);
    }

    [TestTimeout(TestTimeouts.Network)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetPositionsAsync_ReturnsCurrentPositions()
    {
        // Arrange
        var mockPositions = new List<IPosition>
        {
            CreateMockPosition("AAPL", 100, 150m),
            CreateMockPosition("MSFT", 50, 300m)
        };

        // Set up the mock to return the positions for any call to ListPositionsAsync
        _mockTradingClient.Setup(x => x.ListPositionsAsync(default(CancellationToken)))
            .ReturnsAsync(mockPositions);

        // Act
        var result = await _marketDataService.GetPositionsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(p => p.Symbol == "AAPL");
        result.Should().Contain(p => p.Symbol == "MSFT");

        // Verify the mock was called
        _mockTradingClient.Verify(x => x.ListPositionsAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Network)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFillsAsync_ReturnsRecentOrders()
    {
        // Arrange
        var mockOrders = new List<IOrder>
        {
            CreateMockOrder("AAPL", OrderSide.Buy, 100, 150m),
            CreateMockOrder("MSFT", OrderSide.Sell, 50, 300m)
        };

        // Set up the mock to return the orders for any call to ListOrdersAsync
        // The Alpaca SDK method returns IEnumerable<IOrder>, which gets converted to List via .ToList()
        _mockTradingClient.Setup(x => x.ListOrdersAsync(
            It.Is<ListOrdersRequest>(r => r.OrderStatusFilter == OrderStatusFilter.All), default(CancellationToken)))
            .ReturnsAsync(mockOrders);

        // Act
        var result = await _marketDataService.GetRecentFillsAsync(10);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(o => o.Symbol == "AAPL");
        result.Should().Contain(o => o.Symbol == "MSFT");

        // Verify the mock was called
        _mockTradingClient.Verify(x => x.ListOrdersAsync(
            It.Is<ListOrdersRequest>(r => r.OrderStatusFilter == OrderStatusFilter.All), default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetStockBarsAsync_WhenAlpacaThrows_ThrowsException()
    {
        // Arrange
        var symbol = "INVALID";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(default(HistoricalBarsRequest), default))
            .ThrowsAsync(new InvalidOperationException("API Error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _marketDataService.GetStockBarsAsync($1, $2, $3)]
    [Fact]
    public void GetIndexValueAsync_TestPlaceholder_PassesForNow()
    {
        // Note: This is a placeholder test for the GetIndexValueAsync method
        // In a real implementation, you'd need to properly mock HttpClient using HttpClientFactory
        // and test the actual HTTP calls and JSON parsing

        // For now, we'll just verify the service can be instantiated
        var mockIndexCacheService = new Mock<IIndexCacheService>();
        var mockStockBarCacheService = new Mock<IStockBarCacheService>();
        var mockVixFallbackService = new Mock<IVixFallbackService>();
        var service = new MarketDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            mockIndexCacheService.Object,
            mockStockBarCacheService.Object,
            _mockLogger.Object,
            mockVixFallbackService.Object);

        service.Should().NotBeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void PolygonTimestampConversion_ShouldConvertCorrectly()
    {
        // Arrange - Test timestamp conversion from Polygon milliseconds to UTC DateTime
        var polygonTimestampMs = 1640995200000L; // January 1, 2022 00:00:00 UTC
        var expectedUtcDateTime = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        // Act - Simulate the conversion that happens in TryParsePolygonBar/TryParseIndexBar
        var convertedDateTime = DateTimeOffset.FromUnixTimeMilliseconds(polygonTimestampMs).UtcDateTime;

        // Assert
        convertedDateTime.Should().Be(expectedUtcDateTime);
        convertedDateTime.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void PolygonTimestampConversion_ShouldHandleMarketHours()
    {
        // Arrange - Test market hours timestamp (9:30 AM EST = 2:30 PM UTC on trading day)
        // Let's use a known timestamp: 1640959800000L = December 31, 2021 14:10:00 UTC
        var marketOpenTimestampMs = 1640959800000L;
        var expectedUtcDateTime = new DateTime(2021, 12, 31, 14, 10, 0, DateTimeKind.Utc);

        // Act
        var convertedDateTime = DateTimeOffset.FromUnixTimeMilliseconds(marketOpenTimestampMs).UtcDateTime;

        // Assert
        convertedDateTime.Should().Be(expectedUtcDateTime);
        convertedDateTime.Kind.Should().Be(DateTimeKind.Utc);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void PolygonBar_ShouldMaintainTimezoneConsistencyWithAlpacaBars()
    {
        // Arrange - Create mock Alpaca bar and Polygon bar for same timestamp
        var testTimestamp = new DateTime(2022, 1, 1, 15, 30, 0, DateTimeKind.Utc);

        var mockAlpacaBar = new Mock<IBar>();
        mockAlpacaBar.Setup(x => x.TimeUtc).Returns(testTimestamp);
        mockAlpacaBar.Setup(x => x.Close).Returns(100m);

        var polygonBar = new PolygonBar(DateTime.UtcNow, 100m, 105m, 99m, 102m, 1000, 1000);
        var polygonBarWrapper = new PolygonBarWrapper(polygonBar, "TEST");

        // Act & Assert - Both should have identical UTC timestamps
        polygonBarWrapper.TimeUtc.Should().Be(mockAlpacaBar.Object.TimeUtc);
        polygonBarWrapper.TimeUtc.Kind.Should().Be(DateTimeKind.Utc);
        mockAlpacaBar.Object.TimeUtc.Kind.Should().Be(DateTimeKind.Utc);
    }

    private static List<IBar> CreateMockBars(int count)
    {
        // Use optimized test data factory - allow the requested count for this test
        return TestDataFactory.CreateMinimalBars("TEST", count);
    }

    private static IPosition CreateMockPosition(string symbol, decimal quantity, decimal marketValue)
    {
        var mockPosition = new Mock<IPosition>();
        mockPosition.Setup(x => x.Symbol).Returns(symbol);
        mockPosition.Setup(x => x.Quantity).Returns(quantity);
        mockPosition.Setup(x => x.MarketValue).Returns(marketValue);
        mockPosition.Setup(x => x.UnrealizedProfitLoss).Returns(marketValue * 0.05m); // 5% unrealized gain
        return mockPosition.Object;
    }

    private static IOrder CreateMockOrder(string symbol, Alpaca.Markets.OrderSide side, decimal quantity, decimal price)
    {
        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.Symbol).Returns(symbol);
        mockOrder.Setup(x => x.OrderSide).Returns(side);
        mockOrder.Setup(x => x.Quantity).Returns(quantity);
        mockOrder.Setup(x => x.AverageFillPrice).Returns(price);
        mockOrder.Setup(x => x.FilledAtUtc).Returns(DateTime.UtcNow.AddMinutes(-30));
        return mockOrder.Object;
    }
}
