using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Text.Json;
using CsvHelper;
using System.Globalization;
using ScottPlot;

namespace SmaTrendFollower.Backtesting.Replay;

/// <summary>
/// Core engine for running tick-level backtesting with replay functionality
/// </summary>
public class BacktestReplayEngine
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly PolygonTickLoader _tickLoader;
    private readonly IConnectionMultiplexer _redis;
    private readonly IVirtualTimeProvider _timeProvider;
    private readonly ILogger<BacktestReplayEngine> _logger;

    // Backtesting state
    private decimal _initialCapital = 100_000m;
    private decimal _currentCash;
    private readonly Dictionary<string, decimal> _positions = new();
    private readonly List<BacktestTrade> _trades = new();
    private readonly List<EquityCurvePoint> _equityCurve = new();

    public BacktestReplayEngine(
        ISignalGenerator signalGenerator,
        ITradeExecutor tradeExecutor,
        PolygonTickLoader tickLoader,
        IConnectionMultiplexer redis,
        IVirtualTimeProvider timeProvider,
        ILogger<BacktestReplayEngine> logger)
    {
        _signalGenerator = signalGenerator;
        _tradeExecutor = tradeExecutor;
        _tickLoader = tickLoader;
        _redis = redis;
        _timeProvider = timeProvider;
        _logger = logger;
    }

    /// <summary>
    /// Runs a complete backtest replay with tick-level data
    /// </summary>
    public async Task<BacktestSummary> RunAsync(
        DateTime startDate,
        DateTime endDate,
        string[] symbols,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting backtest replay from {StartDate} to {EndDate} with symbols: {Symbols}",
            startDate, endDate, string.Join(", ", symbols));

        try
        {
            // Initialize backtest state
            InitializeBacktest();

            // Load and merge tick data
            var events = await LoadAndMergeTickDataAsync(symbols, startDate, endDate, cancellationToken);
            _logger.LogInformation("Loaded {Count} total events for replay", events.Count);

            // Run the replay simulation
            await RunReplaySimulationAsync(events, cancellationToken);

            // Compute final results
            var summary = ComputeBacktestSummary(startDate, endDate, symbols);

            // Save results to Redis and generate chart
            await SaveResultsAsync(summary, cancellationToken);

            _logger.LogInformation("Backtest replay completed. Total P&L: {PnL:C}, Trades: {Trades}",
                summary.TotalPnl, summary.Trades);

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during backtest replay");
            throw;
        }
        finally
        {
            // Reset to real time
            _timeProvider.ResetToRealTime();
        }
    }

    private void InitializeBacktest()
    {
        _currentCash = _initialCapital;
        _positions.Clear();
        _trades.Clear();
        _equityCurve.Clear();

        _logger.LogDebug("Initialized backtest with {Capital:C} starting capital", _initialCapital);
    }

    private async Task<List<IReplayEvent>> LoadAndMergeTickDataAsync(
        string[] symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var allEvents = new List<IReplayEvent>();

        foreach (var symbol in symbols)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            _logger.LogInformation("Loading tick data for {Symbol}", symbol);

            // Load trades
            await foreach (var trade in _tickLoader.LoadTradesAsync(symbol, startDate, endDate, cancellationToken))
            {
                allEvents.Add(trade);
            }

            // Load quotes
            await foreach (var quote in _tickLoader.LoadQuotesAsync(symbol, startDate, endDate, cancellationToken))
            {
                allEvents.Add(quote);
            }
        }

        // Sort all events by timestamp
        allEvents.Sort((a, b) => a.TimestampUtc.CompareTo(b.TimestampUtc));

        return allEvents;
    }

    private async Task RunReplaySimulationAsync(
        List<IReplayEvent> events,
        CancellationToken cancellationToken)
    {
        var processedEvents = 0;
        var lastEquityUpdate = DateTime.MinValue;

        foreach (var evt in events)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            // Set virtual time for this event
            _timeProvider.SetVirtualTime(evt.TimestampUtc);

            // Process the event
            await ProcessEventAsync(evt);

            // Update equity curve periodically (every hour)
            if (evt.TimestampUtc - lastEquityUpdate > TimeSpan.FromHours(1))
            {
                UpdateEquityCurve(evt.TimestampUtc);
                lastEquityUpdate = evt.TimestampUtc;
            }

            processedEvents++;
            if (processedEvents % 100000 == 0)
            {
                _logger.LogDebug("Processed {Count} events, current time: {Time}",
                    processedEvents, evt.TimestampUtc);
            }
        }

        _logger.LogInformation("Completed processing {Count} events", processedEvents);
    }

    private async Task ProcessEventAsync(IReplayEvent evt)
    {
        switch (evt)
        {
            case TradeEvent trade:
                await ProcessTradeEventAsync(trade);
                break;
            case QuoteEvent quote:
                await ProcessQuoteEventAsync(quote);
                break;
        }
    }

    private async Task ProcessTradeEventAsync(TradeEvent trade)
    {
        // Update latest price and potentially generate signals
        // This is a simplified implementation - in reality you'd integrate with your signal generation logic
        try
        {
            // Simulate signal generation based on trade
            // You would integrate this with your actual ISignalGenerator
            await Task.CompletedTask; // Placeholder for signal processing
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing trade event for {Symbol}", trade.Symbol);
        }
    }

    private async Task ProcessQuoteEventAsync(QuoteEvent quote)
    {
        // Update bid/ask spreads and market microstructure
        try
        {
            // Simulate quote processing
            // You would integrate this with your actual quote processing logic
            await Task.CompletedTask; // Placeholder for quote processing
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing quote event for {Symbol}", quote.Symbol);
        }
    }

    private void UpdateEquityCurve(DateTime timestamp)
    {
        var currentEquity = CalculateCurrentEquity();
        var drawdown = CalculateDrawdown(currentEquity);

        _equityCurve.Add(new EquityCurvePoint(
            Date: timestamp,
            Equity: currentEquity,
            DrawdownPercent: drawdown
        ));
    }

    private decimal CalculateCurrentEquity()
    {
        // Simplified equity calculation
        // In reality, you'd use current market prices for positions
        return _currentCash + _positions.Values.Sum();
    }

    private decimal CalculateDrawdown(decimal currentEquity)
    {
        if (_equityCurve.Count == 0)
            return 0m;

        var peak = _equityCurve.Max(p => p.Equity);
        return peak > 0 ? (peak - currentEquity) / peak : 0m;
    }

    private BacktestSummary ComputeBacktestSummary(DateTime startDate, DateTime endDate, string[] symbols)
    {
        var finalEquity = CalculateCurrentEquity();
        var totalPnl = finalEquity - _initialCapital;
        var totalReturn = _initialCapital > 0 ? (double)(totalPnl / _initialCapital) : 0.0;

        var winningTrades = _trades.Count(t => t.PnL > 0);
        var winRate = _trades.Count > 0 ? (double)winningTrades / _trades.Count : 0.0;

        var maxDrawdown = _equityCurve.Count > 0 ? (double)_equityCurve.Max(p => p.DrawdownPercent) : 0.0;

        // Simplified Sharpe ratio calculation
        var dailyReturns = CalculateDailyReturns();
        var sharpe = CalculateSharpeRatio(dailyReturns);

        return new BacktestSummary(
            TotalPnl: totalPnl,
            Sharpe: sharpe,
            MaxDrawdown: maxDrawdown,
            Trades: _trades.Count,
            WinRate: winRate,
            StartDate: startDate,
            EndDate: endDate,
            InitialCapital: _initialCapital,
            FinalCapital: finalEquity,
            Symbols: symbols
        );
    }

    private List<double> CalculateDailyReturns()
    {
        var dailyReturns = new List<double>();
        var dailyEquity = _equityCurve
            .GroupBy(p => p.Date.Date)
            .Select(g => new { Date = g.Key, Equity = g.Last().Equity })
            .OrderBy(d => d.Date)
            .ToList();

        for (int i = 1; i < dailyEquity.Count; i++)
        {
            var prevEquity = dailyEquity[i - 1].Equity;
            var currentEquity = dailyEquity[i].Equity;
            
            if (prevEquity > 0)
            {
                var dailyReturn = (double)((currentEquity - prevEquity) / prevEquity);
                dailyReturns.Add(dailyReturn);
            }
        }

        return dailyReturns;
    }

    private double CalculateSharpeRatio(List<double> dailyReturns)
    {
        if (dailyReturns.Count < 2)
            return 0.0;

        var avgReturn = dailyReturns.Average();
        var stdDev = Math.Sqrt(dailyReturns.Select(r => Math.Pow(r - avgReturn, 2)).Average());

        return stdDev > 0 ? avgReturn / stdDev * Math.Sqrt(252) : 0.0; // Annualized
    }

    private async Task SaveResultsAsync(BacktestSummary summary, CancellationToken cancellationToken)
    {
        try
        {
            // Save summary to Redis
            var database = _redis.GetDatabase();
            var summaryJson = JsonSerializer.Serialize(summary);
            await database.StringSetAsync("backtest:last", summaryJson, TimeSpan.FromDays(7));

            // Generate equity curve chart
            await GenerateEquityCurveChartAsync();

            // Save CSV results (optional)
            await SaveCsvResultsAsync(summary, cancellationToken);

            _logger.LogInformation("Saved backtest results to Redis, generated chart, and saved CSV");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving backtest results");
        }
    }

    private async Task GenerateEquityCurveChartAsync()
    {
        try
        {
            // Ensure wwwroot/backtest directory exists
            var wwwrootDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
            var backtestDir = Path.Combine(wwwrootDir, "backtest");
            Directory.CreateDirectory(backtestDir);

            if (_equityCurve.Count == 0)
            {
                _logger.LogWarning("No equity curve data available for chart generation");
                return;
            }

            // Prepare data for ScottPlot
            var dates = _equityCurve.Select(p => p.Date.ToOADate()).ToArray();
            var equity = _equityCurve.Select(p => (double)p.Equity).ToArray();

            // Create the plot
            var plt = new Plot();
            plt.Add.Scatter(dates, equity);
            plt.Axes.DateTimeTicksBottom();

            plt.Title("Backtest Equity Curve");
            plt.XLabel("Date");
            plt.YLabel("Portfolio Value ($)");

            // Style the plot
            plt.Axes.SetLimitsY(equity.Min() * 0.95, equity.Max() * 1.05);
            plt.ShowLegend();

            // Save the chart
            var chartPath = Path.Combine(backtestDir, "curve.png");
            plt.SavePng(chartPath, 800, 400);

            _logger.LogInformation("Generated equity curve chart at {ChartPath}", chartPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate equity curve chart");
        }
    }

    private async Task SaveCsvResultsAsync(BacktestSummary summary, CancellationToken cancellationToken)
    {
        try
        {
            var resultsDir = Path.Combine(Directory.GetCurrentDirectory(), "Results");
            Directory.CreateDirectory(resultsDir);

            var fileName = $"backtest_{summary.StartDate:yyyyMMdd}_{summary.EndDate:yyyyMMdd}.csv";
            var filePath = Path.Combine(resultsDir, fileName);

            using var writer = new StringWriter();
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            await csv.WriteRecordsAsync(_trades, cancellationToken);
            await File.WriteAllTextAsync(filePath, writer.ToString(), cancellationToken);

            _logger.LogInformation("Saved CSV results to {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to save CSV results");
        }
    }
}
