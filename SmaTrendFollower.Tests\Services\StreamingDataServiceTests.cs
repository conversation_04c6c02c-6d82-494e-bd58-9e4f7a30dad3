using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class StreamingDataServiceTests
{
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<ILogger<StreamingDataService>> _mockLogger;
    private readonly Mock<IAlpacaStreamingClient> _mockAlpacaStreamingClient;
    private readonly Mock<IAlpacaDataStreamingClient> _mockAlpacaDataStreamingClient;
    private readonly Mock<IPolygonWebSocketClient> _mockPolygonWebSocketClient;
    private readonly StreamingDataService _service;

    public StreamingDataServiceTests()
    {
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockLogger = new Mock<ILogger<StreamingDataService>>();
        _mockAlpacaStreamingClient = new Mock<IAlpacaStreamingClient>();
        _mockAlpacaDataStreamingClient = new Mock<IAlpacaDataStreamingClient>();
        _mockPolygonWebSocketClient = new Mock<IPolygonWebSocketClient>();

        // Setup factory methods
        _mockAlpacaFactory.Setup(f => f.CreateStreamingClient()).Returns(_mockAlpacaStreamingClient.Object);
        _mockAlpacaFactory.Setup(f => f.CreateDataStreamingClient()).Returns(_mockAlpacaDataStreamingClient.Object);
        _mockPolygonFactory.Setup(f => f.CreateWebSocketClient()).Returns(_mockPolygonWebSocketClient.Object);

        _service = new StreamingDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Constructor_ShouldInitializeWithDisconnectedStatus()
    {
        // Assert
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectAlpacaStreamAsync_ShouldCreateClientsAndConnect()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        // Act
        await _service.ConnectAlpacaStreamAsync();

        // Assert
        _mockAlpacaFactory.Verify(f => f.CreateStreamingClient(), Times.Once);
        _mockAlpacaFactory.Verify(f => f.CreateDataStreamingClient(), Times.Once);
        _mockAlpacaStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(default(CancellationToken)), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(default(CancellationToken)), Times.Once);
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Connected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectAlpacaStreamAsync_WhenAlreadyConnected_ShouldNotReconnect()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        // First connection
        await _service.ConnectAlpacaStreamAsync();

        // Act - Second connection attempt
        await _service.ConnectAlpacaStreamAsync();

        // Assert - Should only connect once
        _mockAlpacaStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(default(CancellationToken)), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectAlpacaStreamAsync_WhenConnectionFails_ShouldSetErrorStatus()
    {
        // Arrange
        var exception = new Exception("Connection failed");
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).ThrowsAsync(exception);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.ConnectAlpacaStreamAsync());
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Error);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectPolygonStreamAsync_ShouldCreateClientAndConnect()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        // Act
        await _service.ConnectPolygonStreamAsync();

        // Assert
        _mockPolygonFactory.Verify(f => f.CreateWebSocketClient(), Times.Once);
        _mockPolygonWebSocketClient.Verify(c => c.ConnectAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToQuotesAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToQuotesAsync(symbols));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToQuotesAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToQuotesAsync(symbols);

        // Assert - Since we're using placeholder implementation, just verify no exception is thrown
        // In real implementation, this would verify actual subscription calls
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToBarsAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToBarsAsync(symbols));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToBarsAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToBarsAsync(symbols);

        // Assert - Since we're using placeholder implementation, just verify no exception is thrown
        // In real implementation, this would verify actual subscription calls
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToTradeUpdatesAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToTradeUpdatesAsync());
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToTradeUpdatesAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToTradeUpdatesAsync();

        // Assert - Since we're using placeholder implementation, just verify no exception is thrown
        // In real implementation, this would verify actual subscription calls
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToIndexUpdatesAsync(symbols));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.SubscribeToIndexUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .Returns(Task.CompletedTask);

        await _service.ConnectPolygonStreamAsync();

        // Act
        await _service.SubscribeToIndexUpdatesAsync(symbols);

        // Assert
        _mockPolygonWebSocketClient.Verify(c => c.SubscribeToIndexUpdatesAsync(symbols, default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task DisconnectAllAsync_ShouldDisconnectAllClients()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        _mockAlpacaStreamingClient.Setup(c => c.DisconnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockAlpacaDataStreamingClient.Setup(c => c.DisconnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.DisconnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        await _service.ConnectAlpacaStreamAsync();
        await _service.ConnectPolygonStreamAsync();

        // Act
        await _service.DisconnectAllAsync();

        // Assert
        _mockAlpacaStreamingClient.Verify(c => c.DisconnectAsync(default(CancellationToken)), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.DisconnectAsync(default(CancellationToken)), Times.Once);
        _mockPolygonWebSocketClient.Verify(c => c.DisconnectAsync(default(CancellationToken)), Times.Once);
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UnsubscribeAllAsync_ShouldUnsubscribeFromAllStreams()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.UnsubscribeAllAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);

        // Act
        await _service.UnsubscribeAllAsync();

        // Assert - Should not throw even without connections
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        _service.Dispose(); // Should not throw
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Events_ShouldBeRaisable()
    {
        // Arrange
        StreamingQuoteEventArgs? quoteArgs = null;
        StreamingBarEventArgs? barArgs = null;
        IndexUpdateEventArgs? indexArgs = null;
        TradeUpdateEventArgs? tradeArgs = null;

        _service.QuoteReceived += (sender, args) => quoteArgs = args;
        _service.BarReceived += (sender, args) => barArgs = args;
        _service.IndexUpdated += (sender, args) => indexArgs = args;
        _service.TradeUpdated += (sender, args) => tradeArgs = args;

        // Act - Events would be triggered by actual data
        // For testing, we verify they can be subscribed to

        // Assert
        quoteArgs.Should().BeNull(); // No events fired yet
        barArgs.Should().BeNull();
        indexArgs.Should().BeNull();
        tradeArgs.Should().BeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToSecondBarsAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToSecondBarsAsync(symbols));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToSecondBarsAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToSecondBarsAsync(symbols);

        // Assert - Should not throw
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToVixUpdatesAsync_ShouldCompleteSuccessfully()
    {
        // Act
        await _service.SubscribeToVixUpdatesAsync();

        // Assert - Should not throw (placeholder implementation)
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToOptionsQuotesAsync_ShouldCompleteSuccessfully()
    {
        // Arrange
        var optionSymbols = new[] { "AAPL240119C00150000", "MSFT240119P00200000" };

        // Act
        await _service.SubscribeToOptionsQuotesAsync(optionSymbols);

        // Assert - Should not throw (placeholder implementation)
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectPolygonStreamAsync_WhenConnectionFails_ShouldThrowException()
    {
        // Arrange
        var exception = new Exception("Polygon connection failed");
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).ThrowsAsync(exception);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.ConnectPolygonStreamAsync());
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectPolygonStreamAsync_WhenAlreadyConnected_ShouldNotReconnect()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        // First connection
        await _service.ConnectPolygonStreamAsync();

        // Act - Second connection attempt
        await _service.ConnectPolygonStreamAsync();

        // Assert - Should only connect once
        _mockPolygonWebSocketClient.Verify(c => c.ConnectAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_WithPolygonConnectionFailure_ShouldThrowException()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };
        var exception = new Exception("Subscription failed");

        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.SubscribeToIndexUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .ThrowsAsync(exception);

        await _service.ConnectPolygonStreamAsync();

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.SubscribeToIndexUpdatesAsync(symbols));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task DisconnectAllAsync_WithoutConnections_ShouldCompleteSuccessfully()
    {
        // Act
        await _service.DisconnectAllAsync();

        // Assert
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task DisconnectAllAsync_WithConnectionErrors_ShouldHandleGracefully()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        _mockAlpacaStreamingClient.Setup(c => c.DisconnectAsync(default(CancellationToken))).ThrowsAsync(new Exception("Disconnect failed"));
        _mockAlpacaDataStreamingClient.Setup(c => c.DisconnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        await _service.ConnectAlpacaStreamAsync();

        // Act - Should not throw despite disconnect error
        await _service.DisconnectAllAsync();

        // Assert
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UnsubscribeAllAsync_WithPolygonConnection_ShouldUnsubscribe()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.UnsubscribeAllAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        await _service.ConnectPolygonStreamAsync();

        // Act
        await _service.UnsubscribeAllAsync();

        // Assert
        _mockPolygonWebSocketClient.Verify(c => c.UnsubscribeAllAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task UnsubscribeAllAsync_WithPolygonError_ShouldHandleGracefully()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.UnsubscribeAllAsync(default(CancellationToken)))
            .ThrowsAsync(new Exception("Unsubscribe failed"));

        await _service.ConnectPolygonStreamAsync();

        // Act - Should not throw
        await _service.UnsubscribeAllAsync();

        // Assert - Should complete without throwing
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectAlpacaStreamAsync_WithPartialFailure_ShouldSetErrorStatus()
    {
        // Arrange - One client succeeds, one fails
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).ThrowsAsync(new Exception("Data client failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.ConnectAlpacaStreamAsync());
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Error);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectAlpacaStreamAsync_WithUnauthorizedStatus_ShouldThrowException()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Unauthorized));

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _service.ConnectAlpacaStreamAsync());
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Error);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToQuotesAsync_ShouldTrackSubscribedSymbols()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "TSLA" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToQuotesAsync(symbols);

        // Assert - Verify symbols are tracked (this tests internal state management)
        // Since we can't directly access private fields, we test through behavior
        await _service.SubscribeToQuotesAsync(symbols); // Should not fail on re-subscription
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToBarsAsync_ShouldTrackSubscribedSymbols()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "TSLA" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToBarsAsync(symbols);

        // Assert - Verify symbols are tracked
        await _service.SubscribeToBarsAsync(symbols); // Should not fail on re-subscription
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToTradeUpdatesAsync_ShouldTrackSubscriptionState()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToTradeUpdatesAsync();

        // Assert - Verify subscription is tracked
        await _service.SubscribeToTradeUpdatesAsync(); // Should not fail on re-subscription
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_ShouldTrackSubscribedSymbols()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.SubscribeToIndexUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .Returns(Task.CompletedTask);

        await _service.ConnectPolygonStreamAsync();

        // Act
        await _service.SubscribeToIndexUpdatesAsync(symbols);

        // Assert - Verify symbols are tracked
        await _service.SubscribeToIndexUpdatesAsync(symbols); // Should not fail on re-subscription
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Dispose_ShouldClearAllSubscriptions()
    {
        // Arrange - Service is already created in constructor

        // Act
        _service.Dispose();

        // Assert
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert - Should not throw on multiple dispose calls
        _service.Dispose();
        _service.Dispose();
        _service.Dispose();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectAlpacaStreamAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        // Act - Simulate concurrent connection attempts
        var tasks = new List<Task>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.ConnectAlpacaStreamAsync());
        }

        await Task.WhenAll(tasks);

        // Assert - Should only connect once despite multiple calls
        _mockAlpacaStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(default(CancellationToken)), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(default(CancellationToken)), Times.Once);
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Connected);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ConnectPolygonStreamAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(default(CancellationToken))).Returns(Task.CompletedTask);

        // Act - Simulate concurrent connection attempts
        var tasks = new List<Task>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.ConnectPolygonStreamAsync());
        }

        await Task.WhenAll(tasks);

        // Assert - Should only connect once despite multiple calls
        _mockPolygonWebSocketClient.Verify(c => c.ConnectAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToQuotesAsync_WithEmptyArray_ShouldHandleCorrectly()
    {
        // Arrange
        var symbols = new string[0];
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act & Assert - Should not throw
        await _service.SubscribeToQuotesAsync(symbols);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToQuotesAsync_WithSingleSymbol_ShouldHandleCorrectly()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act & Assert - Should not throw
        await _service.SubscribeToQuotesAsync(symbols);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task SubscribeToQuotesAsync_WithMultipleSymbols_ShouldHandleCorrectly()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "TSLA", "NVDA", "GOOGL" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(default(CancellationToken))).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act & Assert - Should not throw
        await _service.SubscribeToQuotesAsync(symbols);
    }
}
