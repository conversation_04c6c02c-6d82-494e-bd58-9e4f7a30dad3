using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for TickStreamService
/// Tests real-time tick streaming functionality, Redis caching, and event handling
/// </summary>
public class TickStreamServiceTests : IDisposable
{
    private readonly Mock<IPolygonWebSocketClient> _mockPolygonClient;
    private readonly PolygonWebSocketManager _polygonWsManager;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<ILogger<TickStreamService>> _mockLogger;
    private readonly Mock<ILogger<PolygonWebSocketManager>> _mockWsManagerLogger;
    private readonly TickStreamService _tickStreamService;

    public TickStreamServiceTests()
    {
        _mockPolygonClient = new Mock<IPolygonWebSocketClient>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockLogger = new Mock<ILogger<TickStreamService>>();
        _mockWsManagerLogger = new Mock<ILogger<PolygonWebSocketManager>>();

        // Create real PolygonWebSocketManager with mock dependencies
        _polygonWsManager = new PolygonWebSocketManager(
            _mockRedisService.Object,
            _mockWsManagerLogger.Object,
            "test-api-key");

        _tickStreamService = new TickStreamService(
            _mockPolygonClient.Object,
            _polygonWsManager,
            _mockRedisService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Assert
        _tickStreamService.Status.Should().Be(TickStreamStatus.Disconnected);
        _tickStreamService.SubscribedSymbols.Should().BeEmpty();
    }

    [Fact]
    public void Constructor_WithNullPolygonClient_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new TickStreamService(null!, _polygonWsManager, _mockRedisService.Object, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("polygonClient");
    }

    [Fact]
    public void Constructor_WithNullRedisService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new TickStreamService(_mockPolygonClient.Object, _polygonWsManager, null!, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("redisService");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new TickStreamService(_mockPolygonClient.Object, _polygonWsManager, _mockRedisService.Object, null!);
        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public async Task StartAsync_WhenNotStarted_ShouldConnectToPolygonAndSetStatusToConnected()
    {
        // Arrange
        _mockPolygonClient.Setup(x => x.ConnectAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);

        // Act
        await _tickStreamService.StartAsync();

        // Assert
        _mockRedisService.Verify(x => x.GetDatabaseAsync(default(int)), Times.Once);
        _mockPolygonClient.Verify(x => x.ConnectAsync(default(CancellationToken)), Times.Once);
    }

    [Fact]
    public async Task StartAsync_WhenAlreadyActive_ShouldNotConnectAgain()
    {
        // Arrange
        _mockPolygonClient.Setup(x => x.ConnectAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);

        // Start once
        await _tickStreamService.StartAsync();

        // Act - Start again
        await _tickStreamService.StartAsync();

        // Assert - Should only connect once
        _mockPolygonClient.Verify(x => x.ConnectAsync(default(CancellationToken)), Times.Once);
    }

    [Fact]
    public async Task StopAsync_WhenActive_ShouldDisconnectFromPolygon()
    {
        // Arrange
        _mockPolygonClient.Setup(x => x.ConnectAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);
        _mockPolygonClient.Setup(x => x.DisconnectAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);
        _mockPolygonClient.Setup(x => x.UnsubscribeAllAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);

        await _tickStreamService.StartAsync();

        // Act
        await _tickStreamService.StopAsync();

        // Assert
        _mockPolygonClient.Verify(x => x.UnsubscribeAllAsync(default(CancellationToken)), Times.Once);
        _mockPolygonClient.Verify(x => x.DisconnectAsync(default(CancellationToken)), Times.Once);
    }

    [Fact]
    public async Task SubscribeAsync_WithValidSymbols_ShouldSubscribeToPolygonStreams()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        var dataTypes = TickDataTypes.All;

        _mockPolygonClient.Setup(x => x.SubscribeToTradeUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .Returns(Task.CompletedTask);
        _mockPolygonClient.Setup(x => x.SubscribeToQuoteUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .Returns(Task.CompletedTask);
        _mockPolygonClient.Setup(x => x.SubscribeToAggregateUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .Returns(Task.CompletedTask);

        // Act
        await _tickStreamService.SubscribeAsync(symbols, dataTypes);

        // Assert
        _mockPolygonClient.Verify(x => x.SubscribeToTradeUpdatesAsync(
            It.Is<IEnumerable<string>>(s => s.SequenceEqual(symbols)), 
            default(CancellationToken)), Times.Once);
        _mockPolygonClient.Verify(x => x.SubscribeToQuoteUpdatesAsync(
            It.Is<IEnumerable<string>>(s => s.SequenceEqual(symbols)), 
            default(CancellationToken)), Times.Once);
        _mockPolygonClient.Verify(x => x.SubscribeToAggregateUpdatesAsync(
            It.Is<IEnumerable<string>>(s => s.SequenceEqual(symbols)), 
            default(CancellationToken)), Times.Once);

        _tickStreamService.SubscribedSymbols.Should().Contain(symbols);
    }

    [Fact]
    public async Task SubscribeAsync_WithTradesOnly_ShouldOnlySubscribeToTrades()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        var dataTypes = TickDataTypes.Trades;

        _mockPolygonClient.Setup(x => x.SubscribeToTradeUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)))
            .Returns(Task.CompletedTask);

        // Act
        await _tickStreamService.SubscribeAsync(symbols, dataTypes);

        // Assert
        _mockPolygonClient.Verify(x => x.SubscribeToTradeUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)), Times.Once);
        _mockPolygonClient.Verify(x => x.SubscribeToQuoteUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)), Times.Never);
        _mockPolygonClient.Verify(x => x.SubscribeToAggregateUpdatesAsync(It.IsAny<IEnumerable<string>>(), default(CancellationToken)), Times.Never);
    }

    [Fact]
    public async Task UnsubscribeAllAsync_WhenCalled_ShouldUnsubscribeFromPolygon()
    {
        // Arrange
        _mockPolygonClient.Setup(x => x.UnsubscribeAllAsync(default(CancellationToken)))
            .Returns(Task.CompletedTask);

        // Act
        await _tickStreamService.UnsubscribeAllAsync();

        // Assert
        _mockPolygonClient.Verify(x => x.UnsubscribeAllAsync(default(CancellationToken)), Times.Once);
        _tickStreamService.SubscribedSymbols.Should().BeEmpty();
    }

    [Fact]
    public async Task GetLatestTradeAsync_WithNoData_ShouldReturnNull()
    {
        // Act
        var result = await _tickStreamService.GetLatestTradeAsync("AAPL");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetLatestQuoteAsync_WithNoData_ShouldReturnNull()
    {
        // Act
        var result = await _tickStreamService.GetLatestQuoteAsync("AAPL");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetRecentTradesAsync_WithNoData_ShouldReturnEmptyList()
    {
        // Act
        var result = await _tickStreamService.GetRecentTradesAsync("AAPL");

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetRecentQuotesAsync_WithNoData_ShouldReturnEmptyList()
    {
        // Act
        var result = await _tickStreamService.GetRecentQuotesAsync("AAPL");

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task IsAboveFiveMinuteHighAsync_WithNoData_ShouldReturnFalse()
    {
        // Act
        var result = await _tickStreamService.IsAboveFiveMinuteHighAsync("AAPL");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task CalculateVwapAsync_WithNoData_ShouldReturnNull()
    {
        // Act
        var result = await _tickStreamService.CalculateVwapAsync("AAPL");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetBidAskSpreadAsync_WithNoData_ShouldReturnNull()
    {
        // Act
        var result = await _tickStreamService.GetBidAskSpreadAsync("AAPL");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void Dispose_WhenCalled_ShouldNotThrow()
    {
        // Act & Assert
        var act = () => _tickStreamService.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public async Task StartAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _tickStreamService.Dispose();

        // Act & Assert
        var act = async () => await _tickStreamService.StartAsync();
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    public void Dispose()
    {
        _tickStreamService?.Dispose();
    }
}
