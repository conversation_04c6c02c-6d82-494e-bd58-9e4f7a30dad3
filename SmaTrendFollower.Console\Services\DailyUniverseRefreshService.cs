using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using System.Diagnostics;
using static SmaTrendFollower.Services.RedisKeyConstants;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for daily universe refresh service
/// </summary>
public interface IDailyUniverseRefreshService
{
    /// <summary>
    /// Manually trigger universe refresh
    /// </summary>
    Task<RedisUniverseCandidates> RefreshUniverseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current cached candidates
    /// </summary>
    Task<RedisUniverseCandidates?> GetCurrentCandidatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if candidates cache is valid
    /// </summary>
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get service status and next refresh time
    /// </summary>
    Task<UniverseRefreshStatus> GetStatusAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Background service that refreshes the trading universe daily at 8:30 AM ET
/// Filters symbols by volume, volatility, and price to get top ~200 candidates
/// </summary>
public sealed class DailyUniverseRefreshService : BackgroundService, IDailyUniverseRefreshService, IDisposable
{
    private readonly IPolygonSymbolUniverseService _symbolUniverseService;
    private readonly IPolygonSymbolSnapshotService _snapshotService;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<DailyUniverseRefreshService> _logger;
    private readonly UniverseRefreshConfig _config;
    private readonly Timer _refreshTimer;

    private DateTime _lastRefreshTime = DateTime.MinValue;
    private DateTime _nextRefreshTime = DateTime.MinValue;
    private bool _isRefreshing = false;

    public DailyUniverseRefreshService(
        IPolygonSymbolUniverseService symbolUniverseService,
        IPolygonSymbolSnapshotService snapshotService,
        IConfiguration configuration,
        ILogger<DailyUniverseRefreshService> logger)
    {
        _symbolUniverseService = symbolUniverseService ?? throw new ArgumentNullException(nameof(symbolUniverseService));
        _snapshotService = snapshotService ?? throw new ArgumentNullException(nameof(snapshotService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load configuration
        _config = UniverseRefreshConfig.Default;
        configuration.GetSection("UniverseRefresh").Bind(_config);

        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl = configuration["REDIS_URL"];
            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis connection established for universe candidates caching");
            }
            else
            {
                _logger.LogInformation("Redis not configured - universe candidates caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - universe candidates caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }

        // Calculate next refresh time
        _nextRefreshTime = CalculateNextRefreshTime();

        // Create timer for daily refresh
        var timeUntilNextRefresh = _nextRefreshTime - DateTime.UtcNow;
        if (timeUntilNextRefresh < TimeSpan.Zero)
        {
            timeUntilNextRefresh = TimeSpan.FromMinutes(1); // Start soon if we're past today's refresh time
        }

        _refreshTimer = new Timer(OnTimerElapsed, null, timeUntilNextRefresh, TimeSpan.FromDays(1));

        _logger.LogInformation("DailyUniverseRefreshService initialized. Next refresh: {NextRefresh} UTC ({TimeUntil} from now)",
            _nextRefreshTime, timeUntilNextRefresh);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("DailyUniverseRefreshService started");

        // Check if we need to refresh on startup
        var shouldRefreshOnStartup = await ShouldRefreshOnStartupAsync(stoppingToken);
        if (shouldRefreshOnStartup)
        {
            _logger.LogInformation("Performing startup universe refresh");
            try
            {
                await RefreshUniverseAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during startup universe refresh");
            }
        }

        // Keep the service running
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Check every 5 minutes
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("DailyUniverseRefreshService stopped");
    }

    public async Task<RedisUniverseCandidates> RefreshUniverseAsync(CancellationToken cancellationToken = default)
    {
        if (_isRefreshing)
        {
            _logger.LogWarning("Universe refresh already in progress, skipping");
            var current = await GetCurrentCandidatesAsync(cancellationToken);
            return current ?? new RedisUniverseCandidates();
        }

        _isRefreshing = true;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting daily universe refresh");

            // Get full symbol list
            var symbols = await _symbolUniverseService.GetSymbolListAsync(cancellationToken);
            var symbolList = symbols.ToList();

            _logger.LogInformation("Retrieved {SymbolCount} symbols from universe service", symbolList.Count);

            // Filter and rank symbols
            var criteria = new CandidateFilterCriteria
            {
                MinPrice = _config.MinPrice,
                MinAverageVolume = _config.MinAverageVolume,
                MinVolatilityPercent = _config.MinVolatilityPercent,
                MaxCandidates = _config.MaxCandidates,
                AnalysisPeriodDays = _config.AnalysisPeriodDays,
                MinMarketCap = _config.MinMarketCap,
                ExcludedExchanges = _config.ExcludedExchanges,
                ExcludedTypes = _config.ExcludedTypes
            };

            var candidates = await _snapshotService.FilterAndRankSymbolsAsync(symbolList, criteria, cancellationToken);
            var candidateList = candidates.ToList();

            stopwatch.Stop();

            // Create metrics
            var metrics = new CandidateGenerationMetrics
            {
                GenerationTime = stopwatch.Elapsed,
                ApiCallCount = 0, // This would be tracked by the snapshot service
                ErrorCount = 0,
                FilterBreakdown = new Dictionary<string, int>
                {
                    ["TotalSymbols"] = symbolList.Count,
                    ["QualifiedCandidates"] = candidateList.Count
                },
                AverageProcessingTime = TimeSpan.FromMilliseconds(stopwatch.Elapsed.TotalMilliseconds / symbolList.Count),
                DataFreshness = new Dictionary<string, DateTime>
                {
                    ["SymbolList"] = DateTime.UtcNow,
                    ["Snapshots"] = DateTime.UtcNow
                }
            };

            // Create candidates object
            var universeCandidates = new RedisUniverseCandidates
            {
                Candidates = candidateList,
                GeneratedAt = DateTime.UtcNow,
                EvaluatedCount = symbolList.Count,
                CandidateCount = candidateList.Count,
                FilterCriteria = criteria,
                Metrics = metrics,
                Metadata = $"Generated {candidateList.Count} candidates from {symbolList.Count} symbols in {stopwatch.Elapsed.TotalSeconds:F1}s"
            };

            // Cache the results
            if (_redis != null)
            {
                await CacheCandidatesAsync(universeCandidates);
            }

            _lastRefreshTime = DateTime.UtcNow;
            _nextRefreshTime = CalculateNextRefreshTime();

            _logger.LogInformation("Universe refresh completed: {CandidateCount}/{SymbolCount} candidates in {ElapsedMs}ms. Next refresh: {NextRefresh}",
                candidateList.Count, symbolList.Count, stopwatch.ElapsedMilliseconds, _nextRefreshTime);

            return universeCandidates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during universe refresh");
            throw;
        }
        finally
        {
            _isRefreshing = false;
        }
    }

    public async Task<RedisUniverseCandidates?> GetCurrentCandidatesAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var candidatesJson = await _redis.StringGetAsync(RedisUniverseCandidates.GetRedisKey());
            if (!candidatesJson.HasValue)
            {
                return null;
            }

            return RedisUniverseCandidates.FromJson(candidatesJson!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving current candidates");
            return null;
        }
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        var candidates = await GetCurrentCandidatesAsync(cancellationToken);
        if (candidates == null)
        {
            return false;
        }

        // Check if generated today and after the refresh time
        var today = DateTime.UtcNow.Date;
        var refreshTimeToday = today.Add(_config.RefreshTimeUtc);

        // Cache is valid if:
        // 1. Generated today after the refresh time, OR
        // 2. Generated yesterday after refresh time and today's refresh hasn't happened yet
        var isValidToday = candidates.GeneratedAt.Date == today && candidates.GeneratedAt >= refreshTimeToday;
        var isValidFromYesterday = candidates.GeneratedAt.Date == today.AddDays(-1) && 
                                   candidates.GeneratedAt >= today.AddDays(-1).Add(_config.RefreshTimeUtc) &&
                                   DateTime.UtcNow < refreshTimeToday;

        return isValidToday || isValidFromYesterday;
    }

    public async Task<UniverseRefreshStatus> GetStatusAsync(CancellationToken cancellationToken = default)
    {
        var candidates = await GetCurrentCandidatesAsync(cancellationToken);
        var isValid = await IsCacheValidAsync(cancellationToken);

        return new UniverseRefreshStatus
        {
            IsRefreshing = _isRefreshing,
            LastRefreshTime = _lastRefreshTime,
            NextRefreshTime = _nextRefreshTime,
            IsCacheValid = isValid,
            CandidateCount = candidates?.CandidateCount ?? 0,
            LastGeneratedAt = candidates?.GeneratedAt,
            ServiceStatus = _isRefreshing ? "Refreshing" : "Idle"
        };
    }

    private async Task<bool> ShouldRefreshOnStartupAsync(CancellationToken cancellationToken)
    {
        // Check if we have valid cached data
        var isValid = await IsCacheValidAsync(cancellationToken);
        if (isValid)
        {
            _logger.LogInformation("Valid universe candidates found in cache, skipping startup refresh");
            return false;
        }

        // Check if it's past the refresh time today
        var now = DateTime.UtcNow;
        var today = now.Date;
        var refreshTimeToday = today.Add(_config.RefreshTimeUtc);

        if (now >= refreshTimeToday)
        {
            _logger.LogInformation("Past today's refresh time and no valid cache, performing startup refresh");
            return true;
        }

        _logger.LogInformation("Before today's refresh time, will wait for scheduled refresh");
        return false;
    }

    private DateTime CalculateNextRefreshTime()
    {
        var now = DateTime.UtcNow;
        var today = now.Date;
        var refreshTimeToday = today.Add(_config.RefreshTimeUtc);

        if (now < refreshTimeToday)
        {
            return refreshTimeToday; // Today's refresh time
        }
        else
        {
            return today.AddDays(1).Add(_config.RefreshTimeUtc); // Tomorrow's refresh time
        }
    }

    private async void OnTimerElapsed(object? state)
    {
        try
        {
            _logger.LogInformation("Daily refresh timer elapsed, starting universe refresh");
            await RefreshUniverseAsync(CancellationToken.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during scheduled universe refresh");
        }
    }

    private async Task CacheCandidatesAsync(RedisUniverseCandidates candidates)
    {
        if (_redis == null)
        {
            return;
        }

        try
        {
            await _redis.StringSetAsync(RedisUniverseCandidates.GetRedisKey(), candidates.ToJson(), RedisKeyConstants.RedisKeyTTL.Universe);

            // Also store with date-specific key for historical tracking
            var dateKey = RedisUniverseCandidates.GetRedisKey(DateTime.UtcNow.Date);
            await _redis.StringSetAsync(dateKey, candidates.ToJson(), TimeSpan.FromDays(7)); // Keep for a week

            _logger.LogDebug("Cached {CandidateCount} candidates with TTL {TTL}h", 
                candidates.CandidateCount, _config.CacheTtlHours);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache universe candidates in Redis");
        }
    }

    public new void Dispose()
    {
        _refreshTimer?.Dispose();
        _connectionMultiplexer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Configuration for universe refresh service
/// </summary>
public class UniverseRefreshConfig
{
    /// <summary>
    /// Time of day to refresh universe (UTC)
    /// </summary>
    public TimeSpan RefreshTimeUtc { get; set; } = new TimeSpan(12, 30, 0); // 8:30 AM ET = 12:30 UTC

    /// <summary>
    /// Minimum price filter
    /// </summary>
    public decimal MinPrice { get; set; } = 10.0m;

    /// <summary>
    /// Minimum average volume filter
    /// </summary>
    public long MinAverageVolume { get; set; } = 1_000_000;

    /// <summary>
    /// Minimum volatility percentage filter
    /// </summary>
    public decimal MinVolatilityPercent { get; set; } = 2.0m;

    /// <summary>
    /// Maximum number of candidates to select
    /// </summary>
    public int MaxCandidates { get; set; } = 200;

    /// <summary>
    /// Analysis period for volume and volatility calculations
    /// </summary>
    public int AnalysisPeriodDays { get; set; } = 20;

    /// <summary>
    /// Minimum market cap filter (optional)
    /// </summary>
    public decimal? MinMarketCap { get; set; }

    /// <summary>
    /// Cache TTL in hours
    /// </summary>
    public int CacheTtlHours { get; set; } = 24;

    /// <summary>
    /// Excluded exchanges
    /// </summary>
    public List<string> ExcludedExchanges { get; set; } = new();

    /// <summary>
    /// Excluded symbol types
    /// </summary>
    public List<string> ExcludedTypes { get; set; } = new();

    /// <summary>
    /// Default configuration
    /// </summary>
    public static UniverseRefreshConfig Default => new()
    {
        RefreshTimeUtc = new TimeSpan(12, 30, 0), // 8:30 AM ET
        MinPrice = 10.0m,
        MinAverageVolume = 1_000_000,
        MinVolatilityPercent = 2.0m,
        MaxCandidates = 200,
        AnalysisPeriodDays = 20,
        CacheTtlHours = 24,
        ExcludedExchanges = new(),
        ExcludedTypes = new()
    };
}

/// <summary>
/// Status information for universe refresh service
/// </summary>
public class UniverseRefreshStatus
{
    public bool IsRefreshing { get; set; }
    public DateTime LastRefreshTime { get; set; }
    public DateTime NextRefreshTime { get; set; }
    public bool IsCacheValid { get; set; }
    public int CandidateCount { get; set; }
    public DateTime? LastGeneratedAt { get; set; }
    public string ServiceStatus { get; set; } = string.Empty;
}
