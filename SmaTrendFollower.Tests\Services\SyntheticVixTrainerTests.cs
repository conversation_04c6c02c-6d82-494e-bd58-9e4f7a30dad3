using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using StackExchange.Redis;
using System.Net;
using System.Text.Json;
using Xunit;
using Quartz;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests.Services;

public class SyntheticVixTrainerTests : IDisposable
{
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ConnectionMultiplexer> _mockConnectionMultiplexer;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly Mock<ILogger<SyntheticVixTrainer>> _mockLogger;
    private readonly Mock<IJobExecutionContext> _mockJobContext;
    private readonly SyntheticVixTrainer _trainer;

    public SyntheticVixTrainerTests()
    {
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockHttpClient = new Mock<HttpClient>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockConnectionMultiplexer = new Mock<ConnectionMultiplexer>();
        _mockDatabase = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<SyntheticVixTrainer>>();
        _mockJobContext = new Mock<IJobExecutionContext>();

        // Setup configuration
        _mockConfiguration.Setup(c => c["POLYGON_API_KEY"]).Returns("test-api-key");

        // Setup Redis
        _mockConnectionMultiplexer.Setup(c => c.GetDatabase(default(int), default(object)))
            .Returns(_mockDatabase.Object);

        // Setup HTTP client factory
        _mockHttpClientFactory.Setup(f => f.CreateClient("Polygon"))
            .Returns(_mockHttpClient.Object);

        // Setup job context
        _mockJobContext.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        _trainer = new SyntheticVixTrainer(
            _mockHttpClientFactory.Object,
            _mockConfiguration.Object,
            _mockConnectionMultiplexer.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Execute_ShouldCallTrainAsync()
    {
        // Arrange
        SetupHistoricalDataResponses();

        // Act & Assert
        await _trainer.Invoking(t => t.Execute(_mockJobContext.Object))
            .Should().NotThrowAsync();
    }

    [Fact]
    public async Task TrainAsync_WithSufficientData_ShouldStoreTrainedWeights()
    {
        // Arrange
        SetupHistoricalDataResponses();

        // Setup Redis expectations
        _mockDatabase.Setup(d => d.StringSetAsync(
            "vix:weights", 
            default(RedisValue), 
            default(TimeSpan?), 
            default(When), 
            default(CommandFlags)))
            .ReturnsAsync(true);

        _mockDatabase.Setup(d => d.StringSetAsync(
            It.Is<RedisKey>(k => k.ToString().StartsWith("vix:weights:")), 
            default(RedisValue), 
            default(TimeSpan?), 
            default(When), 
            default(CommandFlags)))
            .ReturnsAsync(true);

        // Act
        await _trainer.TrainAsync();

        // Assert
        _mockDatabase.Verify(d => d.StringSetAsync(
            "vix:weights", 
            default(RedisValue), 
            TimeSpan.FromDays(30), 
            default(When), 
            default(CommandFlags)), Times.Once);

        _mockDatabase.Verify(d => d.StringSetAsync(
            "vix:weights:a", 
            default(RedisValue), 
            TimeSpan.FromDays(30), 
            default(When), 
            default(CommandFlags)), Times.Once);

        _mockDatabase.Verify(d => d.StringSetAsync(
            "vix:weights:metadata", 
            default(RedisValue), 
            TimeSpan.FromDays(30), 
            default(When), 
            default(CommandFlags)), Times.Once);
    }

    [Fact]
    public async Task TrainAsync_WithInsufficientData_ShouldNotStoreWeights()
    {
        // Arrange
        SetupInsufficientHistoricalDataResponses();

        // Act
        await _trainer.TrainAsync();

        // Assert
        _mockDatabase.Verify(d => d.StringSetAsync(
            "vix:weights", 
            default(RedisValue), 
            default(TimeSpan?), 
            default(When), 
            default(CommandFlags)), Times.Never);
    }

    [Fact]
    public async Task TrainAsync_WithPoorModelQuality_ShouldNotStoreWeights()
    {
        // Arrange - Setup data that would result in poor R-squared
        SetupPoorQualityHistoricalDataResponses();

        // Act
        await _trainer.TrainAsync();

        // Assert
        _mockDatabase.Verify(d => d.StringSetAsync(
            "vix:weights", 
            default(RedisValue), 
            default(TimeSpan?), 
            default(When), 
            default(CommandFlags)), Times.Never);
    }

    private void SetupHistoricalDataResponses()
    {
        var symbols = new[] { "I:VIX", "VXX", "UVXY", "SVXY", "SPY" };
        
        foreach (var symbol in symbols)
        {
            var bars = GenerateRealisticHistoricalData(symbol, 100); // 100 days of data
            var response = new
            {
                results = bars.Select(bar => new
                {
                    c = bar.Close,
                    t = ((DateTimeOffset)bar.Date).ToUnixTimeMilliseconds()
                }).ToArray()
            };

            var responseJson = JsonSerializer.Serialize(response);
            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(responseJson)
            };

            // Note: In a real test, you'd need to properly mock HttpClient
            // This is a simplified version for demonstration
        }
    }

    private void SetupInsufficientHistoricalDataResponses()
    {
        var symbols = new[] { "I:VIX", "VXX", "UVXY", "SVXY", "SPY" };
        
        foreach (var symbol in symbols)
        {
            var bars = GenerateRealisticHistoricalData(symbol, 10); // Only 10 days - insufficient
            var response = new
            {
                results = bars.Select(bar => new
                {
                    c = bar.Close,
                    t = ((DateTimeOffset)bar.Date).ToUnixTimeMilliseconds()
                }).ToArray()
            };

            var responseJson = JsonSerializer.Serialize(response);
            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(responseJson)
            };
        }
    }

    private void SetupPoorQualityHistoricalDataResponses()
    {
        var symbols = new[] { "I:VIX", "VXX", "UVXY", "SVXY", "SPY" };
        
        foreach (var symbol in symbols)
        {
            // Generate random data with no correlation to produce poor R-squared
            var bars = GenerateRandomHistoricalData(symbol, 100);
            var response = new
            {
                results = bars.Select(bar => new
                {
                    c = bar.Close,
                    t = ((DateTimeOffset)bar.Date).ToUnixTimeMilliseconds()
                }).ToArray()
            };

            var responseJson = JsonSerializer.Serialize(response);
            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(responseJson)
            };
        }
    }

    private List<DailyBar> GenerateRealisticHistoricalData(string symbol, int days)
    {
        var bars = new List<DailyBar>();
        var random = new Random(42); // Fixed seed for reproducible tests
        var startDate = DateTime.UtcNow.Date.AddDays(-days);

        for (int i = 0; i < days; i++)
        {
            var date = startDate.AddDays(i);
            decimal basePrice = symbol switch
            {
                "I:VIX" => 20m + (decimal)(random.NextDouble() * 10 - 5), // VIX: 15-25 range
                "VXX" => 25m + (decimal)(random.NextDouble() * 10 - 5),   // VXX: 20-30 range
                "UVXY" => 15m + (decimal)(random.NextDouble() * 10 - 5),  // UVXY: 10-20 range
                "SVXY" => 45m + (decimal)(random.NextDouble() * 10 - 5),  // SVXY: 40-50 range
                "SPY" => 480m + (decimal)(random.NextDouble() * 20 - 10), // SPY: 470-490 range
                _ => 100m
            };

            // Add some correlation between VIX and VXX/UVXY (positive) and SVXY (negative)
            if (symbol == "VXX" || symbol == "UVXY")
            {
                // Positive correlation with VIX
                var vixInfluence = (decimal)(random.NextDouble() * 0.3 + 0.7); // 0.7-1.0 multiplier
                basePrice *= vixInfluence;
            }
            else if (symbol == "SVXY")
            {
                // Negative correlation with VIX
                var vixInfluence = (decimal)(random.NextDouble() * 0.3 + 1.0); // 1.0-1.3 multiplier
                basePrice *= vixInfluence;
            }

            bars.Add(new DailyBar
            {
                Date = date,
                Close = Math.Max(1m, basePrice) // Ensure positive prices
            });
        }

        return bars;
    }

    private List<DailyBar> GenerateRandomHistoricalData(string symbol, int days)
    {
        var bars = new List<DailyBar>();
        var random = new Random(123); // Different seed for random data
        var startDate = DateTime.UtcNow.Date.AddDays(-days);

        for (int i = 0; i < days; i++)
        {
            var date = startDate.AddDays(i);
            var randomPrice = (decimal)(random.NextDouble() * 100 + 10); // Random prices 10-110

            bars.Add(new DailyBar
            {
                Date = date,
                Close = randomPrice
            });
        }

        return bars;
    }

    public void Dispose()
    {
        _trainer?.Dispose();
    }
}
