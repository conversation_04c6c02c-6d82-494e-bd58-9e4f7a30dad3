using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class TradingServiceTests
{
    private readonly Mock<ISignalGenerator> _mockSignalGenerator;
    private readonly Mock<IRiskManager> _mockRiskManager;
    private readonly Mock<IPortfolioGate> _mockPortfolioGate;
    private readonly Mock<ITradeExecutor> _mockTradeExecutor;
    private readonly Mock<IStopManager> _mockStopManager;
    private readonly Mock<ITradingSafetyGuard> _mockSafetyGuard;
    private readonly Mock<IMarketRegimeService> _mockRegimeService;
    private readonly SimpleTradingService _tradingService;

    public TradingServiceTests()
    {
        _mockSignalGenerator = new Mock<ISignalGenerator>();
        _mockRiskManager = new Mock<IRiskManager>();
        _mockPortfolioGate = new Mock<IPortfolioGate>();
        _mockTradeExecutor = new Mock<ITradeExecutor>();
        _mockStopManager = new Mock<IStopManager>();
        _mockSafetyGuard = new Mock<ITradingSafetyGuard>();
        _mockRegimeService = new Mock<IMarketRegimeService>();

        _tradingService = new SimpleTradingService(
            _mockSignalGenerator.Object,
            _mockRiskManager.Object,
            _mockPortfolioGate.Object,
            _mockTradeExecutor.Object,
            _mockStopManager.Object,
            _mockSafetyGuard.Object,
            _mockRegimeService.Object,
            Mock.Of<ILogger<SimpleTradingService>>());
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_ShouldUpdateTrailingStopsFirst()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(default(int))).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradingCycleAsync(), Times.Once);
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(default(CancellationToken)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenSafetyGuardBlocksTrading_ShouldNotProceed()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(false, "Safety check failed", SafetyLevel.Critical));

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradingCycleAsync(), Times.Once);
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(default(CancellationToken)), Times.Never);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Never);
        _mockSignalGenerator.Verify(x => x.RunAsync(default(int)), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenPortfolioGateBlocksTrading_ShouldNotGenerateSignals()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(false);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradingCycleAsync(), Times.Once);
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(default(CancellationToken)), Times.Once);
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(default(CancellationToken)), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(default(int)), Times.Never);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(default(TradingSignal)), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(default(TradingSignal), default(decimal)), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenPortfolioGateAllowsTrading_ShouldGenerateSignals()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradingCycleAsync(), Times.Once);
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(default(CancellationToken)), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WithValidSignals_ShouldCalculateQuantityAndExecuteTrades()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m),
            new TradingSignal("MSFT", 300.00m, 5.00m, 0.20m)
        };

        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(default(TradingSignal))).ReturnsAsync(10m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL")), Times.Once);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT")), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL"), 10m), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT"), 10m), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WithZeroQuantity_ShouldSkipTradeExecution()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m)
        };

        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(default(TradingSignal))).ReturnsAsync(0m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL")), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(default(TradingSignal), default(decimal)), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WithMixedQuantities_ShouldOnlyExecuteValidTrades()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m),
            new TradingSignal("MSFT", 300.00m, 5.00m, 0.20m),
            new TradingSignal("GOOGL", 2500.00m, 50.00m, 0.25m)
        };

        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);

        // Setup different quantities for each signal
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL"))).ReturnsAsync(10m);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT"))).ReturnsAsync(0m);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "GOOGL"))).ReturnsAsync(5m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL"), 10m), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT"), default(decimal)), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "GOOGL"), 5m), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WithNoSignals_ShouldCompleteWithoutErrors()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(default(CancellationToken)), Times.Once);
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(default(CancellationToken)), Times.Once);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(default(TradingSignal)), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(default(TradingSignal), default(decimal)), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WithCancellationToken_ShouldPassTokenToStopManager()
    {
        // Arrange
        var cancellationToken = new CancellationToken();
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(false);
        _mockRegimeService.Setup(x => x.GetCachedRegimeAsync(default(CancellationToken))).ReturnsAsync(MarketRegime.Sideways);

        // Act
        await _tradingService.ExecuteCycleAsync(cancellationToken);

        // Assert
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(cancellationToken), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_ShouldFollowCorrectExecutionOrder()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m)
        };

        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(default(TradingSignal))).ReturnsAsync(10m);

        var callOrder = new List<string>();
        _mockStopManager.Setup(x => x.UpdateTrailingStopsAsync(default(CancellationToken)))
            .Callback(() => callOrder.Add("UpdateTrailingStops"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken)))
            .Callback(() => callOrder.Add("RegimeCheck"))
            .ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync())
            .Callback(() => callOrder.Add("ShouldTrade"))
            .ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(default(int)))
            .Callback(() => callOrder.Add("RunAsync"))
            .ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(default(TradingSignal)))
            .Callback(() => callOrder.Add("CalculateQuantity"))
            .ReturnsAsync(10m);
        _mockTradeExecutor.Setup(x => x.ExecuteTradeAsync(default(TradingSignal), default(decimal)))
            .Callback(() => callOrder.Add("ExecuteTrade"));

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        callOrder.Should().Equal("UpdateTrailingStops", "RegimeCheck", "ShouldTrade", "RunAsync", "CalculateQuantity", "ExecuteTrade");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenRegimeBlocksTrading_ShouldNotGenerateSignals()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(false);
        _mockRegimeService.Setup(x => x.GetCachedRegimeAsync(default(CancellationToken))).ReturnsAsync(MarketRegime.Volatile);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradingCycleAsync(), Times.Once);
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(default(CancellationToken)), Times.Once);
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(default(CancellationToken)), Times.Once);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Never);
        _mockSignalGenerator.Verify(x => x.RunAsync(default(int)), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_WhenRegimeAllowsTrading_ShouldProceedToPortfolioGate()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(default(int))).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRegimeService.Verify(x => x.IsTradingAllowedAsync(default(CancellationToken)), Times.Once);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(default(int)), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteCycleAsync_ShouldCheckRegimeBeforePortfolioGate()
    {
        // Arrange
        _mockSafetyGuard.Setup(x => x.ValidateTradingCycleAsync())
            .ReturnsAsync(new SafetyCheckResult(true, "Safety validation passed"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken))).ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(default(int))).ReturnsAsync(new List<TradingSignal>());

        var callOrder = new List<string>();
        _mockStopManager.Setup(x => x.UpdateTrailingStopsAsync(default(CancellationToken)))
            .Callback(() => callOrder.Add("UpdateTrailingStops"));
        _mockRegimeService.Setup(x => x.IsTradingAllowedAsync(default(CancellationToken)))
            .Callback(() => callOrder.Add("RegimeCheck"))
            .ReturnsAsync(true);
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync())
            .Callback(() => callOrder.Add("PortfolioGate"))
            .ReturnsAsync(true);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        callOrder.Should().StartWith(new[] { "UpdateTrailingStops", "RegimeCheck", "PortfolioGate" });
    }
}
