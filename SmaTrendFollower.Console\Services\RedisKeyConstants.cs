using System;

namespace SmaTrendFollower.Services;

/// <summary>
/// Centralized Redis key patterns and TTL constants for consistent cache management
/// Ensures all Redis keys have appropriate expiration times to prevent memory leaks
/// </summary>
public static class RedisKeyConstants
{
    /// <summary>
    /// Standardized TTL values for different types of Redis keys
    /// All runtime keys should use these constants to ensure consistent expiration
    /// </summary>
    public static class RedisKeyTTL
    {
        /// <summary>
        /// TTL for signal flags and trading signals (24 hours)
        /// Used for: signal:*, breakout:*, microstructure:*
        /// </summary>
        public static readonly TimeSpan Signal = TimeSpan.FromHours(24);

        /// <summary>
        /// TTL for universe and symbol lists (24 hours)
        /// Used for: universe:*, polygon:symbols:*
        /// </summary>
        public static readonly TimeSpan Universe = TimeSpan.FromHours(24);

        /// <summary>
        /// TTL for trailing stops (7 days until explicit delete)
        /// Used for: stop:*, position:*
        /// </summary>
        public static readonly TimeSpan Stop = TimeSpan.FromDays(7);

        /// <summary>
        /// TTL for synthetic VIX data (10 minutes for real-time trading)
        /// Used for: vix:synthetic, vix:weights
        /// </summary>
        public static readonly TimeSpan SyntheticVix = TimeSpan.FromMinutes(10);

        /// <summary>
        /// TTL for VIX data from external sources (15 minutes)
        /// Used for: vix:current, vix:source:*
        /// </summary>
        public static readonly TimeSpan VixData = TimeSpan.FromMinutes(15);

        /// <summary>
        /// TTL for market regime analysis (5 minutes)
        /// Used for: regime:*, index:regime:*
        /// </summary>
        public static readonly TimeSpan Regime = TimeSpan.FromMinutes(5);

        /// <summary>
        /// TTL for breadth analysis data (5 minutes)
        /// Used for: breadth:*
        /// </summary>
        public static readonly TimeSpan Breadth = TimeSpan.FromMinutes(5);

        /// <summary>
        /// TTL for execution metrics and QA data (1 hour)
        /// Used for: execution:*
        /// </summary>
        public static readonly TimeSpan Execution = TimeSpan.FromHours(1);

        /// <summary>
        /// TTL for VWAP calculations (5 minutes)
        /// Used for: vwap:*
        /// </summary>
        public static readonly TimeSpan Vwap = TimeSpan.FromMinutes(5);

        /// <summary>
        /// TTL for volatility metrics (5 minutes)
        /// Used for: volatility:*
        /// </summary>
        public static readonly TimeSpan Volatility = TimeSpan.FromMinutes(5);

        /// <summary>
        /// TTL for pre-market analysis (1 hour)
        /// Used for: premarket:*
        /// </summary>
        public static readonly TimeSpan PreMarket = TimeSpan.FromHours(1);

        /// <summary>
        /// TTL for throttle/block flags (24 hours)
        /// Used for: block:*, throttle:*
        /// </summary>
        public static readonly TimeSpan Throttle = TimeSpan.FromHours(24);

        /// <summary>
        /// TTL for WebSocket subscription lists (24 hours)
        /// Used for: ws:subs:*
        /// </summary>
        public static readonly TimeSpan WebSocketSubscriptions = TimeSpan.FromHours(24);

        /// <summary>
        /// TTL for temporary health check keys (1 minute)
        /// Used for: health_check_*
        /// </summary>
        public static readonly TimeSpan HealthCheck = TimeSpan.FromMinutes(1);

        /// <summary>
        /// TTL for pattern analysis data (4 hours)
        /// Used for: microstructure:patterns:*
        /// </summary>
        public static readonly TimeSpan Patterns = TimeSpan.FromHours(4);
    }

    /// <summary>
    /// Redis key patterns for different data types
    /// Used by cleanup service to identify key types and apply appropriate TTLs
    /// </summary>
    public static class KeyPatterns
    {
        public const string Signal = "signal:*";
        public const string Universe = "universe:*";
        public const string PolygonSymbols = "polygon:symbols:*";
        public const string Stop = "stop:*";
        public const string Position = "position:*";
        public const string VixSynthetic = "vix:synthetic*";
        public const string VixWeights = "vix:weights*";
        public const string VixCurrent = "vix:current*";
        public const string VixSource = "vix:source:*";
        public const string Regime = "regime:*";
        public const string IndexRegime = "index:regime:*";
        public const string Breadth = "breadth:*";
        public const string Execution = "execution:*";
        public const string Vwap = "vwap:*";
        public const string Volatility = "volatility:*";
        public const string PreMarket = "premarket:*";
        public const string Block = "block:*";
        public const string Throttle = "throttle:*";
        public const string HealthCheck = "health_check_*";
        public const string Breakout = "breakout:*";
        public const string Microstructure = "microstructure:*";
        public const string MicrostructurePatterns = "microstructure:patterns:*";
        public const string WebSocketSubscriptions = "ws:subs:*";
    }

    /// <summary>
    /// Maps key patterns to their appropriate TTL values
    /// Used by RedisCleanupService to apply correct expiration times
    /// </summary>
    public static readonly Dictionary<string, TimeSpan> PatternToTTL = new()
    {
        { KeyPatterns.Signal, RedisKeyTTL.Signal },
        { KeyPatterns.Universe, RedisKeyTTL.Universe },
        { KeyPatterns.PolygonSymbols, RedisKeyTTL.Universe },
        { KeyPatterns.Stop, RedisKeyTTL.Stop },
        { KeyPatterns.Position, RedisKeyTTL.Stop },
        { KeyPatterns.VixSynthetic, RedisKeyTTL.SyntheticVix },
        { KeyPatterns.VixWeights, RedisKeyTTL.SyntheticVix },
        { KeyPatterns.VixCurrent, RedisKeyTTL.VixData },
        { KeyPatterns.VixSource, RedisKeyTTL.VixData },
        { KeyPatterns.Regime, RedisKeyTTL.Regime },
        { KeyPatterns.IndexRegime, RedisKeyTTL.Regime },
        { KeyPatterns.Breadth, RedisKeyTTL.Breadth },
        { KeyPatterns.Execution, RedisKeyTTL.Execution },
        { KeyPatterns.Vwap, RedisKeyTTL.Vwap },
        { KeyPatterns.Volatility, RedisKeyTTL.Volatility },
        { KeyPatterns.PreMarket, RedisKeyTTL.PreMarket },
        { KeyPatterns.Block, RedisKeyTTL.Throttle },
        { KeyPatterns.Throttle, RedisKeyTTL.Throttle },
        { KeyPatterns.HealthCheck, RedisKeyTTL.HealthCheck },
        { KeyPatterns.Breakout, RedisKeyTTL.Signal },
        { KeyPatterns.Microstructure, RedisKeyTTL.Signal },
        { KeyPatterns.MicrostructurePatterns, RedisKeyTTL.Patterns },
        { KeyPatterns.WebSocketSubscriptions, RedisKeyTTL.WebSocketSubscriptions }
    };

    /// <summary>
    /// Gets the appropriate TTL for a given Redis key based on its pattern
    /// </summary>
    /// <param name="key">The Redis key to analyze</param>
    /// <returns>The appropriate TTL, or null if no pattern matches</returns>
    public static TimeSpan? GetTTLForKey(string key)
    {
        foreach (var (pattern, ttl) in PatternToTTL)
        {
            // Convert Redis pattern to regex pattern
            var regexPattern = pattern.Replace("*", ".*");
            if (System.Text.RegularExpressions.Regex.IsMatch(key, $"^{regexPattern}$"))
            {
                return ttl;
            }
        }
        return null;
    }
}
