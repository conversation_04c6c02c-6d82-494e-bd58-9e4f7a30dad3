using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Polly;
using System.Net;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class RateLimitPolicyFactoryTests
{
    private readonly Mock<ILogger<RateLimitPolicyFactory>> _mockLogger;
    private readonly RateLimitPolicyFactory _factory;

    public RateLimitPolicyFactoryTests()
    {
        _mockLogger = new Mock<ILogger<RateLimitPolicyFactory>>();
        _factory = new RateLimitPolicyFactory(_mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void CreateAlpacaPolicy_ShouldReturnValidPolicy()
    {
        // Act
        var policy = _factory.CreateAlpacaPolicy();

        // Assert
        policy.Should().NotBeNull();
        policy.Should().BeAssignableTo<IAsyncPolicy<HttpResponseMessage>>();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void CreatePolygonPolicy_ShouldReturnValidPolicy()
    {
        // Act
        var policy = _factory.CreatePolygonPolicy();

        // Assert
        policy.Should().NotBeNull();
        policy.Should().BeAssignableTo<IAsyncPolicy<HttpResponseMessage>>();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task AlpacaPolicy_ShouldRetryOnTooManyRequests()
    {
        // Arrange - Create a fast test policy with minimal delays instead of using the real factory
        var fastRetryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r => r.StatusCode == HttpStatusCode.TooManyRequests)
            .Or<HttpRequestException>()
            .WaitAndRetryAsync(
                retryCount: 3, // Reduced from 5 for faster testing
                sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(1), // Minimal delay
                onRetry: (outcome, timespan, retryCount, context) => { });

        var callCount = 0;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(async () =>
        {
            await fastRetryPolicy.ExecuteAsync(() =>
            {
                callCount++;
                if (callCount <= 2) // Reduced threshold for faster test
                {
                    var response = new HttpResponseMessage(HttpStatusCode.TooManyRequests);
                    return Task.FromResult(response);
                }
                throw new HttpRequestException("Final failure");
            });
        });

        // Should have retried multiple times
        callCount.Should().BeGreaterThan(1);
        callCount.Should().BeLessOrEqualTo(4); // 1 initial + 3 retries = 4 total calls
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task PolygonPolicy_ShouldRetryOnTooManyRequests()
    {
        // Arrange - Create a fast test policy with minimal delays
        var fastRetryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r => r.StatusCode == HttpStatusCode.TooManyRequests)
            .Or<HttpRequestException>()
            .WaitAndRetryAsync(
                retryCount: 3, // Reduced from 5 for faster testing
                sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(1), // Minimal delay
                onRetry: (outcome, timespan, retryCount, context) => { });

        var callCount = 0;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(async () =>
        {
            await fastRetryPolicy.ExecuteAsync(() =>
            {
                callCount++;
                if (callCount <= 2) // Reduced threshold for faster test
                {
                    var response = new HttpResponseMessage(HttpStatusCode.TooManyRequests);
                    return Task.FromResult(response);
                }
                throw new HttpRequestException("Final failure");
            });
        });

        // Should have retried multiple times
        callCount.Should().BeGreaterThan(1);
        callCount.Should().BeLessOrEqualTo(4); // 1 initial + 3 retries = 4 total calls
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task AlpacaPolicy_ShouldSucceedOnValidResponse()
    {
        // Arrange
        var policy = _factory.CreateAlpacaPolicy();
        var expectedResponse = new HttpResponseMessage(HttpStatusCode.OK);

        // Act
        var result = await policy.ExecuteAsync(async () =>
        {
            await Task.Delay(1); // Minimal delay for async simulation
            return expectedResponse;
        });

        // Assert
        result.Should().Be(expectedResponse);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task PolygonPolicy_ShouldSucceedOnValidResponse()
    {
        // Arrange
        var policy = _factory.CreatePolygonPolicy();
        var expectedResponse = new HttpResponseMessage(HttpStatusCode.OK);

        // Act
        var result = await policy.ExecuteAsync(async () =>
        {
            await Task.Delay(1); // Minimal delay for async simulation
            return expectedResponse;
        });

        // Assert
        result.Should().Be(expectedResponse);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
