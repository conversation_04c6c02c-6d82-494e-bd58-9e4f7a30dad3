using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class RiskManagerTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<ILogger<RiskManager>> _mockLogger;
    private readonly RiskManager _riskManager;

    public RiskManagerTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockLogger = new Mock<ILogger<RiskManager>>();

        // Set up rate limit helper
        var mockRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();
        mockRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<decimal>>>(), default(string)))
            .Returns<Func<Task<decimal>>, string>((func, key) => func());

        _mockClientFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockClientFactory.Setup(x => x.GetRateLimitHelper()).Returns(mockRateLimitHelper.Object);

        _riskManager = new RiskManager(_mockClientFactory.Object, _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CalculateQuantityAsync_WithValidSignal_ReturnsCorrectQuantity()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(100000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().BeGreaterThan(0);
        // For $100k account: 0.8% risk = $800, max $600 cap
        // Risk dollars = min(100000 * 0.008, 600) = 600
        // Quantity = 600 / (3 * 150) = 1.33
        // Max position size = 100000 * 0.04 / 150 = 26.67
        // Final quantity = min(1.33, 26.67) = 1.33
        quantity.Should().BeApproximately(1.33m, 0.01m);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CalculateQuantityAsync_WithHighEquity_CapsRiskAt800()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 100m, 2m, 0.10m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(1000000m); // $1M equity

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        // For $1M account (Institutional): 0.6% risk = $6000, max $800 cap
        // Risk dollars = min(1000000 * 0.006, 800) = 800
        // Quantity = 800 / (2 * 100) = 4
        // Max position size = 1000000 * 0.025 / 100 = 250
        // Final quantity = min(4, 250) = 4
        quantity.Should().BeApproximately(4m, 0.01m);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CalculateQuantityAsync_WithNullEquity_ReturnsZero()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns((decimal?)null);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().Be(0);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CalculateQuantityAsync_WithSmallAccount_UsesHigherRiskTolerance()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 100m, 2m, 0.10m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(10000m); // $10k equity (Small account)

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        // For $10k account (Small): 1.2% risk = $120, max $150 cap
        // Risk dollars = min(10000 * 0.012, 150) = 120
        // Quantity = 120 / (2 * 100) = 0.6
        // Max position size = 10000 * 0.06 / 100 = 6
        // Final quantity = min(0.6, 6) = 0.6
        quantity.Should().BeApproximately(0.6m, 0.01m);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task CalculateQuantityAsync_WithVerySmallAccount_UsesHighestRiskTolerance()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 50m, 1m, 0.05m);
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(3000m); // $3k equity (Very Small account)

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var quantity = await _riskManager.CalculateQuantityAsync(signal);

        // Assert
        // For $3k account (Very Small): 1.5% risk = $45, max $75 cap
        // Risk dollars = min(3000 * 0.015, 75) = 45
        // Quantity = 45 / (1 * 50) = 0.9
        // Max position size = 3000 * 0.08 / 50 = 4.8
        // Final quantity = min(0.9, 4.8) = 0.9
        quantity.Should().BeApproximately(0.9m, 0.01m);
    }
}
