using Prometheus;

namespace SmaTrendFollower.Monitoring;

/// <summary>
/// Central registry for all Prometheus metrics used in the SmaTrendFollower system.
/// Provides standardized metrics for trading operations, system performance, and observability.
/// </summary>
public static class MetricsRegistry
{
    // === Trading Metrics ===
    
    /// <summary>
    /// Counter for total trades executed, labeled by side (Buy/Sell)
    /// </summary>
    public static readonly Counter TradesTotal =
        Metrics.CreateCounter("trades_total", "Total number of trades executed", 
            new CounterConfiguration { LabelNames = new[] { "side", "symbol" } });

    /// <summary>
    /// Counter for trading signals generated, labeled by signal type and execution status
    /// </summary>
    public static readonly Counter SignalsTotal =
        Metrics.CreateCounter("signals_total", "Total number of trading signals generated",
            new CounterConfiguration { LabelNames = new[] { "signal_type", "executed" } });

    /// <summary>
    /// Histogram for signal generation latency in milliseconds
    /// </summary>
    public static readonly Histogram SignalLatencyMs =
        Metrics.CreateHistogram("signal_latency_ms", "Signal generation latency in milliseconds",
            new HistogramConfiguration { Buckets = Histogram.LinearBuckets(1, 5, 20) });

    /// <summary>
    /// Gauge for current portfolio value in USD
    /// </summary>
    public static readonly Gauge PortfolioValueUsd =
        Metrics.CreateGauge("portfolio_value_usd", "Current portfolio value in USD");

    /// <summary>
    /// Gauge for current cash balance in USD
    /// </summary>
    public static readonly Gauge CashBalanceUsd =
        Metrics.CreateGauge("cash_balance_usd", "Current cash balance in USD");

    /// <summary>
    /// Gauge for daily P&L in USD
    /// </summary>
    public static readonly Gauge DailyPnlUsd =
        Metrics.CreateGauge("daily_pnl_usd", "Daily profit and loss in USD");

    // === Market Data Metrics ===
    
    /// <summary>
    /// Counter for WebSocket reconnections, labeled by channel
    /// </summary>
    public static readonly Counter WsReconnects =
        Metrics.CreateCounter("websocket_reconnect_total", "Total WebSocket reconnections",
            new CounterConfiguration { LabelNames = new[] { "channel", "reason" } });

    /// <summary>
    /// Gauge for current universe size (number of symbols being monitored)
    /// </summary>
    public static readonly Gauge CurrentUniverseSize =
        Metrics.CreateGauge("universe_size", "Current number of symbols in trading universe");

    /// <summary>
    /// Counter for market data requests, labeled by source and status
    /// </summary>
    public static readonly Counter MarketDataRequests =
        Metrics.CreateCounter("market_data_requests_total", "Total market data requests",
            new CounterConfiguration { LabelNames = new[] { "source", "status" } });

    /// <summary>
    /// Histogram for market data fetch latency in milliseconds
    /// </summary>
    public static readonly Histogram MarketDataLatencyMs =
        Metrics.CreateHistogram("market_data_latency_ms", "Market data fetch latency in milliseconds",
            new HistogramConfiguration { Buckets = Histogram.ExponentialBuckets(1, 2, 15) });

    /// <summary>
    /// Gauge for data staleness in minutes (age of most recent data)
    /// </summary>
    public static readonly Gauge DataStalenessMinutes =
        Metrics.CreateGauge("data_staleness_minutes", "Age of most recent market data in minutes",
            new GaugeConfiguration { LabelNames = new[] { "data_type", "symbol" } });

    // === System Performance Metrics ===
    
    /// <summary>
    /// Counter for API rate limit hits, labeled by service
    /// </summary>
    public static readonly Counter RateLimitHits =
        Metrics.CreateCounter("rate_limit_hits_total", "Total API rate limit hits",
            new CounterConfiguration { LabelNames = new[] { "service", "endpoint" } });

    /// <summary>
    /// Counter for circuit breaker trips, labeled by service
    /// </summary>
    public static readonly Counter CircuitBreakerTrips =
        Metrics.CreateCounter("circuit_breaker_trips_total", "Total circuit breaker trips",
            new CounterConfiguration { LabelNames = new[] { "service", "reason" } });

    /// <summary>
    /// Histogram for Redis operation latency in milliseconds
    /// </summary>
    public static readonly Histogram RedisLatencyMs =
        Metrics.CreateHistogram("redis_latency_ms", "Redis operation latency in milliseconds",
            new HistogramConfiguration { Buckets = Histogram.LinearBuckets(0.1, 0.5, 20) });

    /// <summary>
    /// Counter for Redis cache hits and misses
    /// </summary>
    public static readonly Counter RedisCacheOperations =
        Metrics.CreateCounter("redis_cache_operations_total", "Total Redis cache operations",
            new CounterConfiguration { LabelNames = new[] { "operation", "result" } });

    /// <summary>
    /// Gauge for current Redis connection count
    /// </summary>
    public static readonly Gauge RedisConnections =
        Metrics.CreateGauge("redis_connections", "Current number of Redis connections");

    // === Risk Management Metrics ===
    
    /// <summary>
    /// Counter for risk checks performed, labeled by check type and result
    /// </summary>
    public static readonly Counter RiskChecks =
        Metrics.CreateCounter("risk_checks_total", "Total risk checks performed",
            new CounterConfiguration { LabelNames = new[] { "check_type", "result" } });

    /// <summary>
    /// Counter for trades blocked by risk management
    /// </summary>
    public static readonly Counter TradesBlocked =
        Metrics.CreateCounter("trades_blocked_total", "Total trades blocked by risk management",
            new CounterConfiguration { LabelNames = new[] { "reason" } });

    /// <summary>
    /// Gauge for current position count
    /// </summary>
    public static readonly Gauge CurrentPositions =
        Metrics.CreateGauge("current_positions", "Current number of open positions");

    /// <summary>
    /// Gauge for current exposure as percentage of portfolio
    /// </summary>
    public static readonly Gauge CurrentExposurePercent =
        Metrics.CreateGauge("current_exposure_percent", "Current market exposure as percentage of portfolio");

    // === Error Tracking Metrics ===
    
    /// <summary>
    /// Counter for application errors, labeled by component and error type
    /// </summary>
    public static readonly Counter ApplicationErrors =
        Metrics.CreateCounter("application_errors_total", "Total application errors",
            new CounterConfiguration { LabelNames = new[] { "component", "error_type" } });

    /// <summary>
    /// Counter for order execution failures, labeled by reason
    /// </summary>
    public static readonly Counter OrderFailures =
        Metrics.CreateCounter("order_failures_total", "Total order execution failures",
            new CounterConfiguration { LabelNames = new[] { "reason", "symbol" } });

    // === VIX and Market Regime Metrics ===
    
    /// <summary>
    /// Gauge for current VIX value
    /// </summary>
    public static readonly Gauge CurrentVix =
        Metrics.CreateGauge("current_vix", "Current VIX value");

    /// <summary>
    /// Counter for VIX data source fallbacks
    /// </summary>
    public static readonly Counter VixFallbacks =
        Metrics.CreateCounter("vix_fallbacks_total", "Total VIX data source fallbacks",
            new CounterConfiguration { LabelNames = new[] { "from_source", "to_source" } });

    /// <summary>
    /// Gauge for market regime indicator (0=Bear, 1=Bull, 0.5=Neutral)
    /// </summary>
    public static readonly Gauge MarketRegime =
        Metrics.CreateGauge("market_regime", "Current market regime indicator");

    // === Background Service Metrics ===
    
    /// <summary>
    /// Counter for background service executions
    /// </summary>
    public static readonly Counter BackgroundServiceExecutions =
        Metrics.CreateCounter("background_service_executions_total", "Total background service executions",
            new CounterConfiguration { LabelNames = new[] { "service_name", "status" } });

    /// <summary>
    /// Histogram for background service execution duration
    /// </summary>
    public static readonly Histogram BackgroundServiceDurationMs =
        Metrics.CreateHistogram("background_service_duration_ms", "Background service execution duration in milliseconds",
            new HistogramConfiguration { LabelNames = new[] { "service_name" }, Buckets = Histogram.ExponentialBuckets(100, 2, 15) });
}
