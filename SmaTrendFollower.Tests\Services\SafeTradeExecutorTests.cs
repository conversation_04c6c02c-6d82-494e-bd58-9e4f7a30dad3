using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

public class SafeTradeExecutorTests
{
    private readonly Mock<ITradeExecutor> _mockInnerExecutor;
    private readonly Mock<ITradingSafetyGuard> _mockSafetyGuard;
    private readonly Mock<ILogger<SafeTradeExecutor>> _mockLogger;
    private readonly SafeTradeExecutor _safeTradeExecutor;

    public SafeTradeExecutorTests()
    {
        _mockInnerExecutor = new Mock<ITradeExecutor>();
        _mockSafetyGuard = new Mock<ITradingSafetyGuard>();
        _mockLogger = new Mock<ILogger<SafeTradeExecutor>>();

        _safeTradeExecutor = new SafeTradeExecutor(
            _mockInnerExecutor.Object,
            _mockSafetyGuard.Object,
            _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteTradeAsync_WhenSafetyCheckPasses_ShouldExecuteTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var quantity = 10m;

        _mockSafetyGuard.Setup(x => x.ValidateTradeAsync(signal, quantity))
            .ReturnsAsync(new SafetyCheckResult(true, "All checks passed"));

        // Act
        await _safeTradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradeAsync(signal, quantity), Times.Once);
        _mockInnerExecutor.Verify(x => x.ExecuteTradeAsync(signal, quantity), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteTradeAsync_WhenSafetyCheckFails_ShouldNotExecuteTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var quantity = 10m;

        _mockSafetyGuard.Setup(x => x.ValidateTradeAsync(signal, quantity))
            .ReturnsAsync(new SafetyCheckResult(false, "Safety check failed", SafetyLevel.Warning));

        // Act
        await _safeTradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockSafetyGuard.Verify(x => x.ValidateTradeAsync(signal, quantity), Times.Once);
        _mockInnerExecutor.Verify(x => x.ExecuteTradeAsync(default(TradingSignal), default(decimal)), Times.Never);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteTradeAsync_WhenSafetyCheckFailsWithCriticalLevel_ShouldLogCritical()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var quantity = 10m;

        _mockSafetyGuard.Setup(x => x.ValidateTradeAsync(signal, quantity))
            .ReturnsAsync(new SafetyCheckResult(false, "Critical safety violation", SafetyLevel.Critical));

        // Act
        await _safeTradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Critical,
                default(EventId),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Trade blocked by safety guard")),
                default(Exception),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteTradeAsync_WhenInnerExecutorThrows_ShouldPropagateException()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var quantity = 10m;
        var expectedException = new InvalidOperationException("Trade execution failed");

        _mockSafetyGuard.Setup(x => x.ValidateTradeAsync(signal, quantity))
            .ReturnsAsync(new SafetyCheckResult(true, "All checks passed"));

        _mockInnerExecutor.Setup(x => x.ExecuteTradeAsync(signal, quantity))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _safeTradeExecutor.ExecuteTradeAsync(signal, quantity));

        exception.Should().Be(expectedException);
        _mockSafetyGuard.Verify(x => x.ValidateTradeAsync(signal, quantity), Times.Once);
        _mockInnerExecutor.Verify(x => x.ExecuteTradeAsync(signal, quantity), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task ExecuteTradeAsync_WhenSafetyGuardThrows_ShouldNotExecuteTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150m, 3m, 0.15m);
        var quantity = 10m;

        _mockSafetyGuard.Setup(x => x.ValidateTradeAsync(signal, quantity))
            .ThrowsAsync(new InvalidOperationException("Safety validation failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _safeTradeExecutor.ExecuteTradeAsync(signal, quantity));

        _mockInnerExecutor.Verify(x => x.ExecuteTradeAsync(default(TradingSignal), default(decimal)), Times.Never);
    }
}
